# LPF_3 项目说明

## 项目概述
本项目是一个模拟集成电路设计工具包，专注于两级运算放大器的自动化设计与优化。

## 核心功能

### 1. 运算放大器自动设计 (`opamp_auto_design.m`)
- 根据增益带宽积(GBW)和负载电容(CL)自动计算器件尺寸
- 基于SMIC 180nm工艺参数
- 输出完整的器件尺寸和电路参数

### 2. 智能优化系统 (`opamp_design_with_optimization.m`) - **已更新**

#### 关键改进：补偿电阻Rc的动态更新
**问题描述：**
在原版本中，补偿电阻Rc只在初始设计时计算一次：
```
Rc = (1/gm2 + 1) * 1e-3 kΩ
```

但在优化过程中，当W2发生变化时：
- W2变化 → id2变化 → gm2变化 → Rc应该相应变化
- 原代码中Rc保持不变，导致设计不准确

**解决方案：**
1. **新增`calculate_rc_from_gm2()`函数**：根据实际id2动态计算Rc
2. **W2优化过程中Rc实时更新**：每次W2调整后，根据新的gm2重新计算Rc
3. **双重校准机制**：
   - 仿真前：基于理论估算预更新Rc
   - 仿真后：基于实际测量结果精确更新Rc

#### 优化流程
1. **电流镜优化**：确保id3 ≥ id5
2. **输出摆幅优化**：调整MPM2的VDS到-0.9V
3. **Rc动态跟踪**：在整个优化过程中Rc始终与gm2保持同步

### 3. 使用方法

#### 快速运行
```matlab
run('run_opamp_design.m')
```

#### 自定义参数
编辑`run_opamp_design.m`中的参数：
```matlab
GBW = 150e6;  % 增益带宽积 (Hz)
CL = 4;       % 负载电容 (pF)
```

### 4. 输出文件
- `opamp_design_results.mat`：完整设计参数（包含优化后的Rc）
- `GBCA.sp`：SPICE网表文件
- `GBCA.lis`：仿真结果文件

### 5. 技术特点
- ✅ 智能参数获取（支持多种参数传递方式）
- ✅ 自适应迭代优化算法
- ✅ **Rc动态更新**（跟随gm2变化）
- ✅ 详细的优化过程日志
- ✅ 振荡检测与防护
- ✅ 多级容错机制

## 版本历史

### v2.1 (最新) - Rc动态更新
- ✨ 新增：补偿电阻Rc动态更新功能
- 🔧 修复：W2优化时Rc不更新的问题
- 📈 改进：更精确的电路性能计算
- 📝 文档：完整的技术说明和使用指南

### v2.0 - 智能优化系统
- 电流镜自动优化
- MPM2输出摆幅优化  
- 参数传递机制

### v1.0 - 基础设计功能
- 运放自动设计
- SPICE仿真集成

## 技术支持
如果遇到问题或需要功能扩展，请检查：
1. MATLAB版本兼容性
2. 仿真工具路径设置
3. 工艺库文件完整性

---
*注意：本项目基于学术研究目的开发，请在实际设计中验证所有计算结果。* 