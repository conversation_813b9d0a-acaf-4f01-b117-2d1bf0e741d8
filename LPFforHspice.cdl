.SUBCKT op2_cursor_diff GND I VDD Vb Vin Vip Von Vop
*.PININFO GND:I I:I VDD:I Vb:I Vin:I Vip:I Von:O Vop:O
MNM8 net6 Vb net4 GND n18 W=W1 L=1u m=1
MNM7 net3 net5 net4 GND n18 W=W3/2 L=1u m=1
MNM6 net4 I GND GND n18 W=W3 L=1u m=1
MNM2 Von I GND GND n18 W=W7 L=1u m=1
MNM1 net42 Vin net41 GND n18 W=W1 L=1u m=1
MNM0 net1 Vip net41 GND n18 W=W1 L=1u m=1
MNM3 net41 I GND GND n18 W=W3 L=1u m=1
MNM4 Vop I GND GND n18 W=W7 L=1u m=1
MNM5 I I GND GND n18 W=W5 L=1u m=1
RR6 Vop net5 100K $[RP]
RR5 Von net5 100K $[RP]
RR1 net1 net2 R $[RP]
RR0 net42 net43 R $[RP]
CC2 Vop net5 260f $[CP]
CC3 Von net5 260f $[CP]
CC1 net2 Von Cc $[CP]
CC0 net43 Vop Cc $[CP]
MPM5 net6 net6 VDD VDD p18 W=W0 L=1u m=1
MPM4 net3 net3 VDD VDD p18 W=W0 L=1u m=1
MPM3 Von net1 VDD VDD p18 W=W2 L=1u m=10
MPM1 net42 net3 VDD VDD p18 W=W0 L=1u m=1
MPM0 net1 net3 VDD VDD p18 W=W0 L=1u m=1
MPM2 Vop net42 VDD VDD p18 W=W2 L=1u m=10
.ENDS

************************************************************************
* Library Name: LPF_design_SMIC
* Cell Name:    LPFforHspice
* View Name:    schematic
************************************************************************

.SUBCKT LPFforHspice AVDD AVSS Ibias0 Ibias1 Ibias2 Vb Vin Vip Von Vop
*.PININFO AVDD:I AVSS:I Ibias0:I Ibias1:I Ibias2:I Vb:I Vin:I Vip:I Von:B Vop:B
RR56 net6 Vop R5 $[RP]
RR52 net5 Von R5 $[RP]
RR8 net4 net5 R5 $[RP]
RR9 net3 net6 R5 $[RP]
RR55 net1 Von R3 $[RP]
RR57 Vop net2 R3 $[RP]
RR5 net13 net2 R3 $[RP]
RR4 net12 net1 R3 $[RP]
RR3 net10 net12 R1 $[RP]
RR2 net11 net13 R1 $[RP]
RR7 net10 net4 R1 $[RP]
RR6 net11 net3 R1 $[RP]
RR1 Vin net10 R1/2 $[RP]
RR0 Vip net11 R1/2 $[RP]
CC19 net6 Vop C3 $[CP]
CC18 net5 Von C3 $[CP]
CC2 net2 net4 C2 $[CP]
CC3 net1 net3 C2 $[CP]
CC1 net10 net12 C1 $[CP]
CC0 net11 net13 C1 $[CP]
XI0 AVSS Ibias0 AVDD Vb net10 net11 net13 net12 / op2_cursor_diff
XI1 AVSS Ibias1 AVDD Vb net2 net1 net3 net4 / op2_cursor_diff
XI2 AVSS Ibias2 AVDD Vb net6 net5 Von Vop / op2_cursor_diff
.ENDS