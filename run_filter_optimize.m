% run_filter_optimize.m
% 单独运行低通滤波器优化脚本
% 此脚本允许用户调整滤波器设计指标，然后运行优化
% 作者：AI助手
% 日期：2025/6/5

clc;
clear;
close all;

fprintf('=== 低通滤波器优化工具 ===\n');

%% ========== 用户可配置参数区域 ==========
% 在这里修改您的设计参数

% --- 噪声优化参数 ---
Wc = 2*pi*5.2e5;   % 滤波器截止频率 (rad/s) - 默认约510kHz
max_area = 11612;  % 最大允许总面积 (μm²)

% 显示当前使用的参数值
fprintf('\n==========================================\n');
fprintf('使用的设计参数:\n');
fprintf('  Wc = %.2f rad/s (%.2f kHz)\n', Wc, Wc/(2*pi*1000));
fprintf('  max_area = %.2f μm²\n', max_area);
fprintf('==========================================\n\n');

%% 参数传递与优化执行

% 声明全局变量用于参数传递
global WC_GLOBAL MAX_AREA_GLOBAL;

% 设置全局变量
WC_GLOBAL = Wc;
MAX_AREA_GLOBAL = max_area;

% 同时使用assignin方式传递参数（双保险）
assignin('base', 'Wc_user', Wc);
assignin('base', 'max_area_user', max_area);

% 创建临时参数文件作为第三种传递方式
save('temp_params.mat', 'Wc', 'max_area');
fprintf('已保存参数到临时文件temp_params.mat\n');

% 确保清除旧的优化结果，强制使用新参数重新计算
evalin('base', 'clear opt_R1 opt_R3 opt_R5 opt_C1 opt_C2 opt_C3 opt_min_noise opt_area opt_initial_noise');
fprintf('已清除旧的优化结果，将使用新参数重新计算\n');

% 强制重新生成优化参数文件
if exist('optimal_parameters.mat', 'file')
    delete('optimal_parameters.mat');
    fprintf('已删除旧的optimal_parameters.mat文件，将重新生成\n');
end

% 运行噪声优化脚本
fprintf('\n运行噪声优化脚本...\n');
try
    % 使用run直接运行脚本，确保使用最新的参数
    run('ideal3_optimize0.m');
    fprintf('✓ 噪声优化脚本执行完成\n');
    
    % 验证参数是否被正确使用
    if exist('optimal_parameters.mat', 'file')
        % 优化结果已保存到optimal_parameters.mat文件
    else
        fprintf('✗ 警告: optimal_parameters.mat文件未创建\n');
    end
catch ME
    fprintf('✗ 噪声优化脚本执行失败: %s\n', ME.message);
    fprintf('错误位置: %s 行 %d\n', ME.stack(1).name, ME.stack(1).line);
end

fprintf('\n=== 优化完成 ===\n'); 