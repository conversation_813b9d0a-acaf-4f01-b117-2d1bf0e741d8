% 示例：使用自定义参数进行低通滤波器设计
% 
% 这个示例展示如何修改设计参数以满足特定需求

%% 方法1：直接修改lpf_integrated_design.m中的参数
% 打开lpf_integrated_design.m文件，修改开头的用户配置参数区域
% 例如：
%   GBW = 50e6;        % 改为50MHz
%   CL = 3;            % 改为3pF
%   Wc = 2*pi*1e6;     % 改为1MHz
%   max_area = 15000;  % 改为15000 μm²

%% 方法2：创建自定义设计脚本
% 复制lpf_integrated_design.m的内容到新文件，修改参数
% 这样可以保留多个设计配置

%% 方法3：使用wrapper脚本（推荐用于批量设计）
% 创建一个新的函数文件 run_custom_lpf_design.m：
%
% function run_custom_lpf_design(custom_GBW, custom_CL, custom_Wc, custom_area)
%     % 保存当前工作区
%     assignin('base', 'GBW_custom', custom_GBW);
%     assignin('base', 'CL_custom', custom_CL);
%     assignin('base', 'Wc_custom', custom_Wc);
%     assignin('base', 'max_area_custom', custom_area);
%     
%     % 运行修改后的设计脚本
%     lpf_integrated_design;
% end

%% 示例：不同应用场景的参数配置

% 场景1：高频应用（音频处理）
% GBW = 100e6;       % 100MHz运放
% CL = 2;            % 2pF负载
% Wc = 2*pi*20e3;    % 20kHz截止频率
% max_area = 10000;  % 紧凑设计

% 场景2：低噪声应用（传感器接口）
% GBW = 10e6;        % 10MHz运放
% CL = 10;           % 10pF负载
% Wc = 2*pi*1e3;     % 1kHz截止频率
% max_area = 20000;  % 允许更大面积以降低噪声

% 场景3：通用信号处理
% GBW = 30e6;        % 30MHz运放（默认）
% CL = 5;            % 5pF负载（默认）
% Wc = 2*pi*5.2e5;   % 520kHz截止频率（默认）
% max_area = 11612;  % 标准面积约束（默认）

%% 运行设计
fprintf('=== 低通滤波器设计示例 ===\n');
fprintf('请根据您的需求修改lpf_integrated_design.m中的参数\n');
fprintf('然后运行: lpf_integrated_design\n');
fprintf('\n常见参数调整建议:\n');
fprintf('1. 提高GBW -> 获得更好的高频性能\n');
fprintf('2. 减小CL -> 降低功耗，提高速度\n');
fprintf('3. 调整Wc -> 改变滤波器截止频率\n');
fprintf('4. 增大max_area -> 允许更低噪声的设计\n');

%% 快速测试不同参数
fprintf('\n\n示例参数配置:\n');
fprintf('----------------------------\n');
scenarios = {
    '高频音频', 100e6, 2, 2*pi*20e3, 10000;
    '低噪声传感器', 10e6, 10, 2*pi*1e3, 20000;
    '通用信号', 30e6, 5, 2*pi*5.2e5, 11612;
};

for i = 1:size(scenarios, 1)
    fprintf('\n场景%d: %s\n', i, scenarios{i,1});
    fprintf('  GBW = %.0f MHz\n', scenarios{i,2}/1e6);
    fprintf('  CL = %.1f pF\n', scenarios{i,3});
    fprintf('  fc = %.1f kHz\n', scenarios{i,4}/(2*pi*1e3));
    fprintf('  最大面积 = %.0f μm²\n', scenarios{i,5});
end 