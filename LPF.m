function LPF(W0,W1,W2,W3,W5,W7,L,VDD,VSS,Cc,Ibias0,Ibias1,Ibias2,Rc,R1,R3,R5,C1,C2,C3)
    global pathsp %#ok<GVMIS>

    fp=fopen('LPF.sp','w+');
    fprintf(fp,'%s\n','.title  Low Pass Filter with Three Op-Amps');
    fprintf(fp,'%s\n','.OPTIONs post=2 measfall=0 ingold=2 NOMOD accurate');
    fprintf(fp,'%s\n','.include ''baluninout.sp''');
    fprintf(fp,'\n');
    
    % 定义运放子电路
    fprintf(fp,'%s\n','.SUBCKT op2_cursor_diff GND I VDD Vb Vin Vip Von Vop');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM8 net6 Vb net4 GND n18 W=',W1,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM7 net3 net5 net4 GND n18 W=',W3/2,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM6 net4 I GND GND n18 W=',W3,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM2 Von I GND GND n18 W=',W7,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM1 net42 Vin net41 GND n18 W=',W1,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM0 net1 Vip net41 GND n18 W=',W1,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM3 net41 I GND GND n18 W=',W3,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM4 Vop I GND GND n18 W=',W7,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM5 I I GND GND n18 W=',W5,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'RR6 Vop net5 100K $[RP]\n');
    fprintf(fp,'RR5 Von net5 100K $[RP]\n');
    fprintf(fp,'RR1 net1 net2 %.5fK $[RP]\n', Rc);
    fprintf(fp,'RR0 net42 net43 %.5fK $[RP]\n', Rc);
    fprintf(fp,'CC2 Vop net5 260f $[CP]\n');
    fprintf(fp,'CC3 Von net5 260f $[CP]\n');
    fprintf(fp,'CC1 net2 Von %.5fp $[CP]\n', Cc);
    fprintf(fp,'CC0 net43 Vop %.5fp $[CP]\n', Cc);
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM5 net6 net6 VDD VDD p18 W=',W0,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM4 net3 net3 VDD VDD p18 W=',W0,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM3 Von net1 VDD VDD p18 W=',W2/10,'U ' ,'L=',L,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM1 net42 net3 VDD VDD p18 W=',W0,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM0 net1 net3 VDD VDD p18 W=',W0,'U ' ,'L=',L,'U m=1');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM2 Vop net42 VDD VDD p18 W=',W2/10,'U ' ,'L=',L,'U m=10');
    fprintf(fp,'%s\n','.ENDS');
    fprintf(fp,'\n');
    
    % 定义LPF子电路
    fprintf(fp,'%s\n','************************************************************************');
    fprintf(fp,'%s\n','* Library Name: LPF_design_SMIC');
    fprintf(fp,'%s\n','* Cell Name:    LPFforHspice');
    fprintf(fp,'%s\n','* View Name:    schematic');
    fprintf(fp,'%s\n','************************************************************************');
    fprintf(fp,'\n');
    fprintf(fp,'%s\n','.SUBCKT LPFforHspice AVDD AVSS Ibias0 Ibias1 Ibias2 Vb Vin Vip Von Vop');
    
    % 电阻网络
    fprintf(fp,'RR56 net6 Vop %.5fK $[RP]\n', R5);
    fprintf(fp,'RR52 net5 Von %.5fK $[RP]\n', R5);
    fprintf(fp,'RR8 net4 net5 %.5fK $[RP]\n', R5);
    fprintf(fp,'RR9 net3 net6 %.5fK $[RP]\n', R5);
    fprintf(fp,'RR55 net1 Von %.5fK $[RP]\n', R3);
    fprintf(fp,'RR57 Vop net2 %.5fK $[RP]\n', R3);
    fprintf(fp,'RR5 net13 net2 %.5fK $[RP]\n', R3);
    fprintf(fp,'RR4 net12 net1 %.5fK $[RP]\n', R3);
    fprintf(fp,'RR3 net10 net12 %.5fK $[RP]\n', R1);
    fprintf(fp,'RR2 net11 net13 %.5fK $[RP]\n', R1);
    fprintf(fp,'RR7 net10 net4 %.5fK $[RP]\n', R1);
    fprintf(fp,'RR6 net11 net3 %.5fK $[RP]\n', R1);
    fprintf(fp,'RR1 Vin net10 %.5fK $[RP]\n', R1/2);
    fprintf(fp,'RR0 Vip net11 %.5fK $[RP]\n', R1/2);
    
    % 电容网络
    fprintf(fp,'CC19 net6 Vop %.5fp $[CP]\n', C3);
    fprintf(fp,'CC18 net5 Von %.5fp $[CP]\n', C3);
    fprintf(fp,'CC2 net2 net4 %.5fp $[CP]\n', C2);
    fprintf(fp,'CC3 net1 net3 %.5fp $[CP]\n', C2);
    fprintf(fp,'CC1 net10 net12 %.5fp $[CP]\n', C1);
    fprintf(fp,'CC0 net11 net13 %.5fp $[CP]\n', C1);
    
    % 运放实例
    fprintf(fp,'%s\n','XI0 AVSS Ibias0 AVDD Vb net10 net11 net13 net12 / op2_cursor_diff');
    fprintf(fp,'%s\n','XI1 AVSS Ibias1 AVDD Vb net2 net1 net3 net4 / op2_cursor_diff');
    fprintf(fp,'%s\n','XI2 AVSS Ibias2 AVDD Vb net6 net5 Von Vop / op2_cursor_diff');
    fprintf(fp,'%s\n','.ENDS');
    fprintf(fp,'\n');
    
    % 主电路：实例化LPF子电路和转换器
    fprintf(fp,'%s\n','X1 AVDD AVSS Ibias0 Ibias1 Ibias2 Vcm Vin Vip Von Vop LPFforHspice');
    fprintf(fp,'%s\n','X2 Vi Vcm AVSS Vip Vin balunin');
    fprintf(fp,'%s\n','X3 Vop Von AVSS Vo balunout');
    fprintf(fp,'\n');
    
    % 电源和偏置
    fprintf(fp,'%s%9.5f\n','VAVDD AVDD 0 DC ', VDD);
    fprintf(fp,'%s%9.5f\n','VAVSS AVSS 0 DC ', VSS);
    fprintf(fp,'%s%9.5f\n','VVcm Vcm 0 DC ', 0.9);
    
    % 三个偏置电流源
    fprintf(fp,'IIbias0 AVDD Ibias0 DC %.5fuA\n', Ibias0);
    fprintf(fp,'IIbias1 AVDD Ibias1 DC %.5fuA\n', Ibias1);
    fprintf(fp,'IIbias2 AVDD Ibias2 DC %.5fuA\n', Ibias2);
    
    % 单端输入信号
    fprintf(fp,'VVI Vi 0 DC 0 AC 1 0\n');
    
    % 仿真设置
    fprintf(fp,'%s\n','.TEMP 27');
    fprintf(fp,'%s\n','.OP');
    fprintf(fp,'%s\n','.AC DEC 100 1 100MEG');
    
    % 测量语句 - 测量单端输出
    fprintf(fp,'%s\n','.MEAS AC DCGAIN MAX VDB(Vo)');
    fprintf(fp,'%s\n','.MEAS AC FC3dB WHEN VDB(Vo)="DCGAIN-3"');
    
    % 库文件
    fprintf(fp,'%s\n','.LIB ''ms018_v1p9.lib'' TT');
    
    fprintf(fp,'%s\n','.END');
    fclose(fp);
    
    % 检查HSPICE路径是否设置
    if isempty(pathsp)
        pathsp = 'D:\synopsys\Hspice_P-2019.06-SP1-1\WIN64\hspice.exe';
        fprintf('使用默认HSPICE路径: %s\n', pathsp);
    end
    
    % 使用pathsp变量调用HSPICE
    cmd = [pathsp ' -i LPF.sp -o LPF'];
    system(cmd);
end 