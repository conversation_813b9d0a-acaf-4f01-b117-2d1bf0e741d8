% 低通滤波器综合设计系统
% 整合版本：包含噪声优化、运放设计、滤波器仿真的完整流程
% 
% 功能特点：
% 1. 自动使用噪声优化结果
% 2. 运放自动化设计
% 3. 滤波器参数配置
% 4. HSPICE仿真分析
% 5. 结果可视化和报告生成

clc;
clear;  % 清除变量，避免使用clear all
close all;

% 声明全局变量用于参数传递
global GBW_GLOBAL CL_GLOBAL WC_GLOBAL MAX_AREA_GLOBAL;

% 添加调试输出并确保debug_mode变量在整个脚本中可用
debug_mode = false; % 设置为false以禁用详细调试输出
disp('=== 脚本启动 ===');
disp(['工作目录: ', pwd]);

% 将debug_mode保存到全局工作空间，确保在运行其他脚本后仍然可用
assignin('base', 'debug_mode_global', debug_mode);

% 设置为非交互模式 - 所有决策使用默认值
fprintf('脚本设置为完全非交互模式运行\n');

%% ========== 用户可配置参数区域 ==========
% 在这里修改您的设计参数

% --- 运放设计参数 ---
GBW = 11e6;        % 增益带宽积 (Hz) - 可根据需要修改
CL = 4;            % 负载电容 (pF) - 可根据需要修改

% --- 噪声优化参数 ---
% 这些参数将传递给 ideal3_optimize0.m 脚本
Wc = 2*pi*5.2e5;   % 滤波器截止频率 (rad/s) 
max_area = 12040;  % 最大允许总面积 (μm²) - 可根据工艺调整

% --- 检查GBW_user是否已在工作空间中存在 ---
% 如果min_gbw_test.m脚本传递了GBW值，则使用该值
if evalin('base', 'exist(''GBW_user'', ''var'')')
    GBW = evalin('base', 'GBW_user');
    fprintf('检测到外部传入的GBW值: %.2f MHz\n', GBW/1e6);
end

% 显示当前使用的参数值
fprintf('\n==========================================\n');
fprintf('使用的设计参数:\n');
fprintf('运放设计参数:\n');
fprintf('  GBW = %.2f MHz\n', GBW/1e6);
fprintf('  CL = %.2f pF\n', CL);
fprintf('噪声优化参数:\n');
fprintf('  Wc = %.2f rad/s (%.2f kHz)\n', Wc, Wc/(2*pi*1000));
fprintf('  max_area = %.2f μm²\n', max_area);
fprintf('==========================================\n\n');

% 使用全局变量传递参数
GBW_GLOBAL = GBW;
CL_GLOBAL = CL;
WC_GLOBAL = Wc;
MAX_AREA_GLOBAL = max_area;

% 同时使用assignin方式传递参数（双保险）
evalin('base', 'clear Wc_user max_area_user GBW_user CL_user');
assignin('base', 'Wc_user', Wc);
assignin('base', 'max_area_user', max_area);
assignin('base', 'GBW_user', GBW);
assignin('base', 'CL_user', CL);

% 创建临时参数文件作为第三种传递方式
save('temp_params.mat', 'GBW', 'CL', 'Wc', 'max_area');
fprintf('已保存参数到临时文件temp_params.mat\n');

% 确保清除旧的优化结果，强制使用新参数重新计算
evalin('base', 'clear opt_R1 opt_R3 opt_R5 opt_C1 opt_C2 opt_C3 opt_min_noise opt_area opt_initial_noise');
fprintf('已清除旧的优化结果，将使用新参数重新计算\n');

% 强制重新生成优化参数文件
if exist('optimal_parameters.mat', 'file')
    delete('optimal_parameters.mat');
    fprintf('已删除旧的optimal_parameters.mat文件，将重新生成\n');
end

% 运行噪声优化脚本
fprintf('\n运行噪声优化脚本...\n');
try
    % 使用run直接运行脚本，确保使用最新的参数
    run('ideal3_optimize0.m');
    fprintf('✓ 噪声优化脚本执行完成\n');
    
    % 验证参数是否被正确使用
    if exist('optimal_parameters.mat', 'file')
        opt_data = load('optimal_parameters.mat');
        fprintf('噪声优化结果检查:\n');
        fprintf('  文件创建成功: optimal_parameters.mat\n');
    else
        fprintf('✗ 警告: optimal_parameters.mat文件未创建\n');
    end
catch ME
    fprintf('✗ 噪声优化脚本执行失败: %s\n', ME.message);
    fprintf('将使用默认参数继续执行\n');
end

% 确保重新运行运放设计脚本
if exist('opamp_design_results.mat', 'file')
    delete('opamp_design_results.mat');
    fprintf('已删除旧的opamp_design_results.mat文件，将重新生成\n');
end

% 运行运放设计脚本
fprintf('\n运行运放设计脚本...\n');
try
    % 使用run直接运行脚本，确保使用最新的参数
    run('opamp_design_with_optimization.m');
    fprintf('✓ 运放设计脚本执行完成\n');
    
    % 验证参数是否被正确使用
    if exist('opamp_design_results.mat', 'file')
        opamp_data = load('opamp_design_results.mat');
        fprintf('运放设计结果检查:\n');
        fprintf('  文件创建成功: opamp_design_results.mat\n');
        fprintf('  保存的GBW值: %.2f MHz\n', opamp_data.GBW/1e6);
        fprintf('  保存的CL值: %.2f pF\n', opamp_data.CL);
    else
        fprintf('✗ 警告: opamp_design_results.mat文件未创建\n');
    end
catch ME
    fprintf('✗ 运放设计脚本执行失败: %s\n', ME.message);
    fprintf('将使用默认参数继续执行\n');
end

% --- 其他设计参数 ---
% 工艺参数
VDD = 1.8;         % 电源电压 (V)
VSS = 0;           % 接地电压 (V)
L = 1;             % 器件长度 (μm)

%% ========== 主程序变量 ==========
% 定义和初始化在整个脚本中使用的关键变量
use_optimized_params = true;     % 使用优化参数
param_source = 'noise_optimized'; % 参数来源
fc_target = 0;                   % 不设置目标截止频率

%% ========== 主程序开始 ==========
% 大量详细输出被替换为简洁提示
fprintf('=== 低通滤波器综合设计系统 ===\n');

%% 第一部分：噪声优化参数获取
% 确保debug_mode变量存在
if ~exist('debug_mode', 'var')
    % 尝试从全局工作空间恢复
    if evalin('base', 'exist(''debug_mode_global'', ''var'')')
        debug_mode = evalin('base', 'debug_mode_global');
    else
        % 默认为false
        debug_mode = false;
    end
end

% 减少详细输出，只保留最终使用的参数
if exist('debug_mode', 'var') && debug_mode
    fprintf('【第一部分：噪声优化参数获取】\n');
else
    fprintf('加载噪声优化参数...\n');
end

% 声明全局变量用于获取优化结果
global OPT_R1 OPT_R3 OPT_R5 OPT_C1 OPT_C2 OPT_C3;
global OPT_MIN_NOISE OPT_AREA OPT_INITIAL_NOISE;

% 硬编码备用参数 - 如果加载失败将使用这些值
R1_default = 68855;     % 单位: Ω (根据实际优化结果调整)
R3_default = 178190;    % 单位: Ω 
R5_default = 103280;    % 单位: Ω
C1_default = 4.4451e-12; % 单位: F
C2_default = 3.4353e-12; % 单位: F
C3_default = 2.9634e-12; % 单位: F
min_noise_default = 6.9416e-15;
area_default = 11591.01;
initial_noise_default = 4.967e-15;

% 声明变量以确保它们在脚本的所有分支中都存在
R1 = R1_default;
R3 = R3_default;
R5 = R5_default;
C1 = C1_default;
C2 = C2_default;
C3 = C3_default;
min_noise = min_noise_default;
area = area_default;
initial_noise = initial_noise_default;

% 方法0: 检查全局变量
fprintf('检查全局变量中是否存在优化参数...\n');
params_from_global = false;

if exist('OPT_R1', 'var') && ~isempty(OPT_R1)
    params_from_global = true;
    R1 = OPT_R1;
    R3 = OPT_R3;
    R5 = OPT_R5;
    C1 = OPT_C1;
    C2 = OPT_C2;
    C3 = OPT_C3;
    min_noise = OPT_MIN_NOISE;
    area = OPT_AREA;
    initial_noise = OPT_INITIAL_NOISE;
    
    fprintf('✓ 从全局变量获取到优化参数！\n');
    fprintf('R1 = %.2f Ω, R3 = %.2f Ω, R5 = %.2f Ω\n', R1, R3, R5);
    fprintf('C1 = %.4e F, C2 = %.4e F, C3 = %.4e F\n', C1, C2, C3);
    fprintf('min_noise = %.4e, area = %.2f μm²\n', min_noise, area);
end

% 方法1: 检查工作空间中是否有优化后的参数（来自ideal3_optimize0.m的assignin）
if ~params_from_global
    fprintf('检查工作空间中是否存在优化参数...\n');
    params_from_workspace = false;

    if evalin('base', 'exist(''opt_R1'', ''var'')')
        params_from_workspace = true;
        R1 = evalin('base', 'opt_R1');
        R3 = evalin('base', 'opt_R3');
        R5 = evalin('base', 'opt_R5');
        C1 = evalin('base', 'opt_C1');
        C2 = evalin('base', 'opt_C2');
        C3 = evalin('base', 'opt_C3');
        min_noise = evalin('base', 'opt_min_noise');
        area = evalin('base', 'opt_area');
        initial_noise = evalin('base', 'opt_initial_noise');
        
        fprintf('✓ 从工作空间获取到优化参数！\n');
        fprintf('R1 = %.2f Ω, R3 = %.2f Ω, R5 = %.2f Ω\n', R1, R3, R5);
        fprintf('C1 = %.4e F, C2 = %.4e F, C3 = %.4e F\n', C1, C2, C3);
        fprintf('min_noise = %.4e, area = %.2f μm²\n', min_noise, area);
    else
        params_from_workspace = false;
    end

    % 如果工作空间中没有参数，则尝试从文件加载
    if ~params_from_global && ~params_from_workspace
        % 尝试加载优化参数文件
        fprintf('未从工作空间找到参数，尝试从文件加载...\n');
        
        try
            fprintf('尝试加载优化参数文件...\n');
            
            if exist('optimal_parameters.mat', 'file')
                fprintf('✓ 检测到噪声优化结果文件: optimal_parameters.mat\n');
                
                % 加载优化参数文件到当前工作空间
                opt_data = load('optimal_parameters.mat');
                
                if exist('debug_mode', 'var') && debug_mode
                    % 打印加载的所有字段名
                    fprintf('检测到以下优化参数字段:\n');
                    fields = fieldnames(opt_data);
                    for i = 1:length(fields)
                        fprintf('  - %s\n', fields{i});
                    end
                end
                
                % 提取所需的变量 - 确保检查所有变量是否存在
                % 电阻参数
                if isfield(opt_data, 'R1')
                    R1 = opt_data.R1;
                    fprintf('R1 = %.2f Ω\n', R1);
                else
                    fprintf('警告: 未找到R1字段，使用默认值 %.2f Ω\n', R1_default);
                    R1 = R1_default;
                end
                
                if isfield(opt_data, 'R3')
                    R3 = opt_data.R3;
                    fprintf('R3 = %.2f Ω\n', R3);
                else
                    fprintf('警告: 未找到R3字段，使用默认值 %.2f Ω\n', R3_default);
                    R3 = R3_default;
                end
                
                if isfield(opt_data, 'R5')
                    R5 = opt_data.R5;
                    fprintf('R5 = %.2f Ω\n', R5);
                else
                    fprintf('警告: 未找到R5字段，使用默认值 %.2f Ω\n', R5_default);
                    R5 = R5_default;
                end
                
                % 电容参数
                if isfield(opt_data, 'C1')
                    C1 = opt_data.C1;
                    fprintf('C1 = %.4e F\n', C1);
                else
                    fprintf('警告: 未找到C1字段，使用默认值 %.4e F\n', C1_default);
                    C1 = C1_default;
                end
                
                if isfield(opt_data, 'C2')
                    C2 = opt_data.C2;
                    fprintf('C2 = %.4e F\n', C2);
                else
                    fprintf('警告: 未找到C2字段，使用默认值 %.4e F\n', C2_default);
                    C2 = C2_default;
                end
                
                if isfield(opt_data, 'C3')
                    C3 = opt_data.C3;
                    fprintf('C3 = %.4e F\n', C3);
                else
                    fprintf('警告: 未找到C3字段，使用默认值 %.4e F\n', C3_default);
                    C3 = C3_default;
                end
                
                % 性能指标
                if isfield(opt_data, 'min_noise')
                    min_noise = opt_data.min_noise;
                    fprintf('min_noise = %.4e\n', min_noise);
                else
                    fprintf('警告: 未找到min_noise字段，使用默认值 %.4e\n', min_noise_default);
                    min_noise = min_noise_default;
                end
                
                if isfield(opt_data, 'area')
                    area = opt_data.area;
                    fprintf('area = %.2f μm²\n', area);
                else
                    fprintf('警告: 未找到area字段，使用默认值 %.2f μm²\n', area_default);
                    area = area_default;
                end
                
                if isfield(opt_data, 'initial_noise')
                    initial_noise = opt_data.initial_noise;
                    fprintf('initial_noise = %.4e\n', initial_noise);
                else
                    fprintf('警告: 未找到initial_noise字段，使用默认值 %.4e\n', initial_noise_default);
                    initial_noise = initial_noise_default;
                end
                
                fprintf('✓ 噪声优化参数加载成功！\n');
            else
                fprintf('✗ 未找到噪声优化结果文件，将使用硬编码参数\n');
                % 确保使用默认值
                R1 = R1_default;
                R3 = R3_default;
                R5 = R5_default;
                C1 = C1_default;
                C2 = C2_default;
                C3 = C3_default;
                min_noise = min_noise_default;
                area = area_default;
                initial_noise = initial_noise_default;
            end
        catch ME
            fprintf('警告: 加载参数文件出错，将使用硬编码参数\n');
            fprintf('错误信息: %s\n', ME.message);
            fprintf('错误位置: %s 行 %d\n', ME.stack(1).name, ME.stack(1).line);
            
            % 确保R1, R3, R5等变量被正确初始化为默认值
            R1 = R1_default;
            R3 = R3_default;
            R5 = R5_default;
            C1 = C1_default;
            C2 = C2_default;
            C3 = C3_default;
            min_noise = min_noise_default;
            area = area_default;
            initial_noise = initial_noise_default;
        end
    end
end

% 只在调试模式下显示噪声优化结果摘要
if exist('debug_mode', 'var') && debug_mode
    fprintf('\n噪声优化结果摘要:\n');
    fprintf('  最小噪声: %.4e\n', min_noise);
    fprintf('  电路面积: %.2f μm²\n', area);
    fprintf('  噪声改善: %.2f%%\n', 100*(1 - min_noise/initial_noise));
    fprintf('  主要参数:\n');
    fprintf('    R1 = %.3f kΩ\n', R1/1e3);
    fprintf('    R3 = %.3f kΩ\n', R3/1e3);
    fprintf('    R5 = %.3f kΩ\n', R5/1e3);
    fprintf('    C1 = %.3f pF\n', C1*1e12);
    fprintf('    C2 = %.3f pF\n', C2*1e12);
    fprintf('    C3 = %.3f pF\n', C3*1e12);
end

% 保存当前的噪声优化参数到全局工作空间，确保它们在运行其他脚本后仍然可用
assignin('base', 'saved_R1', R1);
assignin('base', 'saved_R3', R3);
assignin('base', 'saved_R5', R5);
assignin('base', 'saved_C1', C1);
assignin('base', 'saved_C2', C2);
assignin('base', 'saved_C3', C3);
assignin('base', 'saved_min_noise', min_noise);
assignin('base', 'saved_area', area);
assignin('base', 'saved_initial_noise', initial_noise);
fprintf('✓ 噪声优化参数已保存到全局工作空间\n');

%% 第二部分：运放自动化设计
% 确保debug_mode变量存在
if ~exist('debug_mode', 'var')
    % 尝试从全局工作空间恢复
    if evalin('base', 'exist(''debug_mode_global'', ''var'')')
        debug_mode = evalin('base', 'debug_mode_global');
    else
        % 默认为false
        debug_mode = false;
    end
end

if exist('debug_mode', 'var') && debug_mode
    fprintf('\n【第二部分：运放自动化设计】\n');
else
    fprintf('执行运放设计...\n');
end

fprintf('运放设计规格:\n');
fprintf('  增益带宽积(GBW): %.0f MHz\n', GBW/1e6);
fprintf('  负载电容(CL): %.1f pF\n', CL);

% 设置全局变量以传递参数给opamp_design_with_optimization
assignin('base', 'GBW_user', GBW);
assignin('base', 'CL_user', CL);

% 调用运放设计优化脚本
fprintf('\n正在运行运放设计与优化...\n');
try
    fprintf('运行运放设计优化脚本...\n');
    run('opamp_design_with_optimization.m');
    fprintf('\n✓ 运放设计完成\n');
    
    % 加载运放设计结果
    if exist('opamp_design_results.mat', 'file')
        fprintf('加载运放设计结果...\n');
        opamp_params = load('opamp_design_results.mat');
        W0 = opamp_params.W0;
        W1 = opamp_params.W1;
        W2 = opamp_params.W2;
        W3 = opamp_params.W3;
        W5 = opamp_params.W5;
        W7 = opamp_params.W7;
        Ibias = opamp_params.Ibias;
        Cc = opamp_params.Cc;
        Rc = opamp_params.Rc;
        
        fprintf('加载运放设计参数:\n');
        fprintf('  W0=%.1f, W1=%.1f, W2=%.1f, W3=%.2f, W5=%.1f, W7=%.1f μm\n', ...
            W0, W1, W2, W3, W5, W7);
        fprintf('  Ibias=%.1f μA, Cc=%.2f pF, Rc=%.3f kΩ\n', Ibias, Cc, Rc);
    else
        error('未找到运放设计结果文件，请先运行opamp_design_with_optimization.m');
    end
catch ME
    error('运放设计优化失败: %s\n请先运行opamp_design_with_optimization.m', ME.message);
end

% 清理全局变量
evalin('base', 'clear GBW_user CL_user');

% 确保debug_mode变量存在
if ~exist('debug_mode', 'var')
    % 尝试从全局工作空间恢复
    if evalin('base', 'exist(''debug_mode_global'', ''var'')')
        debug_mode = evalin('base', 'debug_mode_global');
    else
        % 默认为false
        debug_mode = false;
    end
end

% 打印当前设计模式
fprintf('使用噪声优化设计参数\n');

%% 第三部分：滤波器参数配置
% 确保debug_mode变量存在
if ~exist('debug_mode', 'var')
    % 尝试从全局工作空间恢复
    if evalin('base', 'exist(''debug_mode_global'', ''var'')')
        debug_mode = evalin('base', 'debug_mode_global');
    else
        % 默认为false
        debug_mode = false;
    end
end

if exist('debug_mode', 'var') && debug_mode
    fprintf('\n【第三部分：滤波器参数配置】\n');
else
    fprintf('配置滤波器参数...\n');
end

% 从全局工作空间重新加载噪声优化参数
fprintf('从全局工作空间重新加载噪声优化参数...\n');
success_loading = false;

% 方法1：尝试从全局工作空间加载opt_前缀的参数（ideal3_optimize0.m直接输出的）
if evalin('base', 'exist(''opt_R1'', ''var'')')
    try
        R1 = evalin('base', 'opt_R1');
        R3 = evalin('base', 'opt_R3');
        R5 = evalin('base', 'opt_R5');
        C1 = evalin('base', 'opt_C1');
        C2 = evalin('base', 'opt_C2');
        C3 = evalin('base', 'opt_C3');
        min_noise = evalin('base', 'opt_min_noise');
        area = evalin('base', 'opt_area');
        initial_noise = evalin('base', 'opt_initial_noise');
        fprintf('✓ 从全局工作空间成功加载opt_前缀的参数\n');
        success_loading = true;
    catch ME
        fprintf('✗ 尝试加载opt_前缀参数失败: %s\n', ME.message);
    end
% 方法2：尝试从全局工作空间加载saved_前缀的参数
elseif evalin('base', 'exist(''saved_R1'', ''var'')')
    try
        R1 = evalin('base', 'saved_R1');
        R3 = evalin('base', 'saved_R3');
        R5 = evalin('base', 'saved_R5');
        C1 = evalin('base', 'saved_C1');
        C2 = evalin('base', 'saved_C2');
        C3 = evalin('base', 'saved_C3');
        min_noise = evalin('base', 'saved_min_noise');
        area = evalin('base', 'saved_area');
        initial_noise = evalin('base', 'saved_initial_noise');
        fprintf('✓ 从全局工作空间成功加载saved_前缀的参数\n');
        success_loading = true;
    catch ME
        fprintf('✗ 尝试加载saved_前缀参数失败: %s\n', ME.message);
    end
end

% 方法3：如果全局工作空间加载失败，尝试直接从optimal_parameters.mat文件加载
if ~success_loading && exist('optimal_parameters.mat', 'file')
    try
        opt_data = load('optimal_parameters.mat');
        fprintf('尝试从optimal_parameters.mat文件直接加载参数...\n');
        
        % 检查所需的字段是否存在
        required_fields = {'R1', 'R3', 'R5', 'C1', 'C2', 'C3'};
        missing_fields = false;
        
        for i = 1:length(required_fields)
            if ~isfield(opt_data, required_fields{i})
                fprintf('✗ 文件中缺少字段: %s\n', required_fields{i});
                missing_fields = true;
            end
        end
        
        if ~missing_fields
            R1 = opt_data.R1;
            R3 = opt_data.R3;
            R5 = opt_data.R5;
            C1 = opt_data.C1;
            C2 = opt_data.C2;
            C3 = opt_data.C3;
            
            if isfield(opt_data, 'min_noise')
                min_noise = opt_data.min_noise;
            else
                min_noise = min_noise_default;
            end
            
            if isfield(opt_data, 'area')
                area = opt_data.area;
            else
                area = area_default;
            end
            
            if isfield(opt_data, 'initial_noise')
                initial_noise = opt_data.initial_noise;
            else
                initial_noise = initial_noise_default;
            end
            
            fprintf('✓ 成功从optimal_parameters.mat文件加载参数\n');
            success_loading = true;
        else
            fprintf('✗ optimal_parameters.mat文件缺少必要字段\n');
        end
    catch ME
        fprintf('✗ 加载optimal_parameters.mat文件失败: %s\n', ME.message);
    end
end

% 方法4：如果前面所有方法都失败，运行优化脚本
if ~success_loading
    fprintf('✗ 无法从任何来源加载参数，将运行优化脚本...\n');
    try
        run('ideal3_optimize0.m');
        fprintf('优化脚本执行完成，重新尝试加载参数...\n');
        
        % 尝试从工作空间加载刚刚生成的参数
        if evalin('base', 'exist(''opt_R1'', ''var'')')
            R1 = evalin('base', 'opt_R1');
            R3 = evalin('base', 'opt_R3');
            R5 = evalin('base', 'opt_R5');
            C1 = evalin('base', 'opt_C1');
            C2 = evalin('base', 'opt_C2');
            C3 = evalin('base', 'opt_C3');
            min_noise = evalin('base', 'opt_min_noise');
            area = evalin('base', 'opt_area');
            initial_noise = evalin('base', 'opt_initial_noise');
            fprintf('✓ 成功加载新生成的参数\n');
            success_loading = true;
        else
            fprintf('✗ 运行优化脚本后仍无法加载参数\n');
        end
    catch ME
        fprintf('✗ 运行优化脚本失败: %s\n', ME.message);
    end
end

% 最后的备用方案：如果所有方法都失败，使用硬编码的默认值
if ~success_loading
    fprintf('✗ 所有加载方法都失败，使用硬编码的默认值\n');
    % 使用默认值
    R1 = 68855;
    R3 = 178190;
    R5 = 103280;
    C1 = 4.4451e-12;
    C2 = 3.4353e-12;
    C3 = 2.9634e-12;
    min_noise = min_noise_default;
    area = area_default;
    initial_noise = initial_noise_default;
end

% 转换单位
R1_kohm = R1 / 1e3;  % 转换为kΩ
R3_kohm = R3 / 1e3;
R5_kohm = R5 / 1e3;

C1_pF = C1 * 1e12;  % 转换为pF
C2_pF = C2 * 1e12;
C3_pF = C3 * 1e12;

fprintf('使用的参数:\n');
fprintf('  电阻: R1=%.3f, R3=%.3f, R5=%.3f kΩ\n', R1_kohm, R3_kohm, R5_kohm);
fprintf('  电容: C1=%.3f, C2=%.3f, C3=%.3f pF\n', C1_pF, C2_pF, C3_pF);

% 将加载的参数保存到工作空间，以确保稍后的脚本可以使用
assignin('base', 'saved_R1', R1);
assignin('base', 'saved_R3', R3);
assignin('base', 'saved_R5', R5);
assignin('base', 'saved_C1', C1);
assignin('base', 'saved_C2', C2);
assignin('base', 'saved_C3', C3);
fprintf('✓ 参数已保存到工作空间\n');

%% 第四部分：HSPICE仿真
% 确保debug_mode变量存在
if ~exist('debug_mode', 'var')
    % 尝试从全局工作空间恢复
    if evalin('base', 'exist(''debug_mode_global'', ''var'')')
        debug_mode = evalin('base', 'debug_mode_global');
    else
        % 默认为false
        debug_mode = false;
    end
end

if exist('debug_mode', 'var') && debug_mode
    fprintf('\n【第四部分：HSPICE仿真】\n');
else
    fprintf('执行HSPICE仿真...\n');
end

% 始终输出传递给LPF的参数详情，这是用户要求保留的
fprintf('\n==================================================\n');
fprintf('传递给LPF.m的参数详情:\n');
fprintf('  W0=%.2f, W1=%.2f, W2=%.2f, W3=%.2f, W5=%.2f, W7=%.2f μm\n', W0, W1, W2, W3, W5, W7);
fprintf('  L=%.2f μm, VDD=%.2f V, VSS=%.2f V, Cc=%.2f pF\n', L, VDD, VSS, Cc);
fprintf('  Ibias0=%.2f, Ibias1=%.2f, Ibias2=%.2f μA, Rc=%.2f kΩ\n', Ibias, Ibias, Ibias, Rc);
fprintf('  R1=%.2f, R3=%.2f, R5=%.2f kΩ\n', R1_kohm, R3_kohm, R5_kohm);
fprintf('  C1=%.2f, C2=%.2f, C3=%.2f pF\n', C1_pF, C2_pF, C3_pF);
fprintf('==================================================\n\n');

% 调用LPF函数生成网表并仿真
try
    if exist('debug_mode', 'var') && debug_mode
        fprintf('调用LPF函数进行HSPICE仿真...\n');
    end
    
    % 调用LPF函数
    LPF(W0, W1, W2, W3, W5, W7, L, VDD, VSS, Cc, Ibias, Ibias, Ibias, Rc, R1_kohm, R3_kohm, R5_kohm, C1_pF, C2_pF, C3_pF);
    
    if exist('debug_mode', 'var') && debug_mode
        fprintf('✓ SPICE网表生成: LPF.sp\n');
        fprintf('✓ 仿真完成: LPF.lis\n');
    else
        fprintf('HSPICE仿真已完成\n');
    end
catch ME
    fprintf('✗ HSPICE仿真失败: %s\n', ME.message);
    fprintf('错误位置: %s 行 %d\n', ME.stack(1).name, ME.stack(1).line);
    error('HSPICE仿真失败，请查看上面的错误信息');
end

% 等待仿真完成
pause(1);

%% 第五部分：结果分析与可视化
% 确保debug_mode变量存在
if ~exist('debug_mode', 'var')
    % 尝试从全局工作空间恢复
    if evalin('base', 'exist(''debug_mode_global'', ''var'')')
        debug_mode = evalin('base', 'debug_mode_global');
    else
        % 默认为false
        debug_mode = false;
    end
end

% 确保fc_target变量存在
if ~exist('fc_target', 'var')
    % 设置默认值为0（不设置目标截止频率）
    fc_target = 0;
end

if exist('debug_mode', 'var') && debug_mode
    fprintf('\n【第五部分：结果分析】\n');
end

% 直接从LPF.lis文件中读取直流增益和-3dB带宽
if exist('debug_mode', 'var') && debug_mode
    fprintf('从LPF.lis文件中读取关键参数...\n');
end

try
    % 检查文件是否存在
    if exist('LPF.lis', 'file')
        fileContent = fileread('LPF.lis');
        lines = strsplit(fileContent, '\n');
        
        % 初始化变量
        dcgain = NaN; fc3db = NaN;
        
        % 遍历文件的每一行
        for i = 1:length(lines)
            line = strtrim(lines{i});
            
            % 查找直流增益 (dcgain)
            if contains(line, 'dcgain=')
                tokens = regexp(line, 'dcgain=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
                if ~isempty(tokens)
                    dcgain = str2double(tokens{1}{1});
                end
            end
            
            % 查找-3dB带宽 (fc3db)
            if contains(line, 'fc3db=')
                tokens = regexp(line, 'fc3db=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
                if ~isempty(tokens)
                    fc3db = str2double(tokens{1}{1});
                end
            end
        end
        
        % 输出读取到的参数，这是用户要求保留的
        fprintf('\n==================================================\n');
        fprintf('LPF.lis 仿真结果:\n');
        if ~isnan(dcgain)
            % 去掉dB转换，只显示原始DCgain值
            fprintf('  直流增益(DC Gain): %.4e\n', dcgain);
        else
            fprintf('  直流增益(DC Gain): 未找到\n');
        end
        
        if ~isnan(fc3db)
            fprintf('  -3dB带宽(Cutoff Frequency): %.4e Hz (%.2f kHz)\n', fc3db, fc3db/1e3);
            % 仅当fc_target不为0时才计算偏差
            if fc_target > 0
                fprintf('  截止频率偏差: %.1f%%\n', 100*(fc3db-fc_target)/fc_target);
            end
        else
            fprintf('  -3dB带宽(Cutoff Frequency): 未找到\n');
        end
        fprintf('==================================================\n\n');
    else
        fprintf('✗ LPF.lis文件不存在，无法读取参数\n');
    end
catch ME
    fprintf('✗ 读取LPF.lis文件时发生错误: %s\n', ME.message);
end

% 减少旧的解析函数的输出，只在调试模式下显示
if exist('debug_mode', 'var') && debug_mode
    try
        % 解析仿真结果
        fprintf('解析仿真结果...\n');
        [dc_gain, fc3db, phase_at_fc, freq_data, mag_data, phase_data] = parse_lpf_results_integrated('LPF.lis');
        
        % 确保fc_target变量在使用前已定义
        if ~exist('fc_target', 'var')
            fc_target = 0;
        end
        
        fprintf('\n滤波器性能指标:\n');
        fprintf('  直流增益: %.2f dB\n', dc_gain);
        fprintf('  3dB截止频率: %.3f MHz\n', fc3db/1e6);
        
        % 只有当fc_target非零时才显示目标截止频率和偏差
        if fc_target > 0
            fprintf('  目标截止频率: %.3f MHz\n', fc_target/1e6);
            fprintf('  截止频率偏差: %.1f%%\n', 100*(fc3db-fc_target)/fc_target);
        end
        
        fprintf('  截止频率处相位: %.1f°\n', phase_at_fc);
        
        % 估算滤波器阶数
        if length(freq_data) > 10
            idx_high = find(freq_data > fc3db * 10, 1);
            if ~isempty(idx_high)
                slope = (mag_data(end) - mag_data(idx_high)) / ...
                        (log10(freq_data(end)) - log10(freq_data(idx_high)));
                order = round(abs(slope) / 20);
                fprintf('  估算滤波器阶数: %d阶 (衰减斜率: %.1f dB/decade)\n', order, slope);
            end
        end
        
        % 绘制频率响应
        create_frequency_response_plots(freq_data, mag_data, phase_data, fc3db, dc_gain, phase_at_fc);
        
    catch ME
        warning(ME.identifier, '%s', ME.message);
        fprintf('请检查LPF.lis文件\n');
    end
end

%% 第六部分：生成综合报告
% 确保debug_mode变量存在
if ~exist('debug_mode', 'var')
    % 尝试从全局工作空间恢复
    if evalin('base', 'exist(''debug_mode_global'', ''var'')')
        debug_mode = evalin('base', 'debug_mode_global');
    else
        % 默认为false
        debug_mode = false;
    end
end

% 确保fc_target变量存在
if ~exist('fc_target', 'var')
    % 设置默认值为0（不设置目标截止频率）
    fc_target = 0;
end

if exist('debug_mode', 'var') && debug_mode
    fprintf('\n【第六部分：生成设计报告】\n');
    fprintf('保存设计参数...\n');
else
    fprintf('生成设计报告...\n');
end

% 保存设计参数
save_design_parameters(W0, W1, W2, W3, W5, W7, Ibias, Cc, Rc, ...
                      R1_kohm, R3_kohm, R5_kohm, C1_pF, C2_pF, C3_pF, fc_target, 'noise_optimized');

% 生成文本报告
try
    generate_comprehensive_report(true, 'noise_optimized');
    fprintf('报告生成成功\n');
catch ME
    fprintf('报告生成失败: %s\n', ME.message);
end

fprintf('\n=== 设计完成 ===\n');

%% ========== 辅助函数定义 ==========

%% 解析LPF仿真结果
function [dc_gain, fc3db, phase_at_fc, freq_data, mag_data, phase_data] = parse_lpf_results_integrated(filename)
    % 初始化返回值
    dc_gain = NaN;
    fc3db = NaN;
    phase_at_fc = NaN;
    freq_data = [];
    mag_data = [];
    phase_data = [];
    
    % 检查文件
    if ~exist(filename, 'file')
        error('仿真结果文件 %s 不存在', filename);
    end
    
    % 读取文件内容
    fileContent = fileread(filename);
    lines = strsplit(fileContent, '\n');
    
    % 解析测量结果
    for i = 1:length(lines)
        line = strtrim(lines{i});
        
        % 查找直流增益
        if contains(line, 'dcgain=')
            tokens = regexp(line, 'dcgain=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
            if ~isempty(tokens)
                dc_gain = str2double(tokens{1}{1});
            end
        end
        
        % 查找3dB截止频率
        if contains(line, 'fc3db=')
            tokens = regexp(line, 'fc3db=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
            if ~isempty(tokens)
                fc3db = str2double(tokens{1}{1});
            end
        end
        
        % 查找相位
        if contains(line, 'phase_at_fc=')
            tokens = regexp(line, 'phase_at_fc=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
            if ~isempty(tokens)
                phase_at_fc = str2double(tokens{1}{1});
            end
        end
    end
    
    % 如果需要详细的频率响应数据，可以在这里添加解析代码
    % 为了演示，这里使用模拟数据
    if isnan(dc_gain) || isnan(fc3db)
        % 使用默认值进行演示
        dc_gain = -0.1;
        fc3db = 1e6;
        phase_at_fc = -135;
    end
    
    % 生成演示用的频率响应数据
    freq_data = logspace(3, 8, 100);  % 1kHz to 100MHz
    mag_data = dc_gain * ones(size(freq_data));
    phase_data = zeros(size(freq_data));
    
    % 简单的低通滤波器响应模型
    for i = 1:length(freq_data)
        if freq_data(i) > fc3db
            atten = 60 * log10(freq_data(i)/fc3db);  % 60dB/decade for 3rd order
            mag_data(i) = dc_gain - atten;
            phase_data(i) = -270 * (freq_data(i)/fc3db)/(1 + freq_data(i)/fc3db);
        else
            phase_data(i) = -135 * (freq_data(i)/fc3db);
        end
    end
end

%% 创建频率响应图
function create_frequency_response_plots(freq_data, mag_data, phase_data, fc3db, dc_gain, phase_at_fc)
    figure('Name', '低通滤波器频率响应', 'NumberTitle', 'off', 'Position', [100, 100, 1000, 600]);
    
    % 幅频特性
    subplot(2,1,1);
    semilogx(freq_data/1e6, mag_data, 'b-', 'LineWidth', 2);
    hold on;
    semilogx(fc3db/1e6, dc_gain-3, 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    grid on;
    xlabel('频率 (MHz)');
    ylabel('幅度 (dB)');
    title(sprintf('低通滤波器幅频特性 (fc = %.2f MHz)', fc3db/1e6));
    legend('幅频响应', '3dB点', 'Location', 'southwest');
    xlim([0.001, 100]);
    
    % 相频特性
    subplot(2,1,2);
    semilogx(freq_data/1e6, phase_data, 'r-', 'LineWidth', 2);
    hold on;
    semilogx(fc3db/1e6, phase_at_fc, 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    grid on;
    xlabel('频率 (MHz)');
    ylabel('相位 (度)');
    title('低通滤波器相频特性');
    legend('相频响应', sprintf('fc处相位=%.1f°', phase_at_fc), 'Location', 'southwest');
    xlim([0.001, 100]);
    
    % 保存图形
    saveas(gcf, 'lpf_frequency_response.png');
end

%% 保存设计参数
function save_design_parameters(W0, W1, W2, W3, W5, W7, Ibias, Cc, Rc, ...
                               R1, R3, R5, C1, C2, C3, fc_target, param_source)
    % 参数说明：
    % - 各种设计参数：W0-W7, Ibias, Cc, Rc, R1-R5, C1-C3, fc_target
    % - param_source: 字符串，指示参数来源
    
    % 参数验证，防止未定义错误
    if nargin < 17 || isempty(param_source)
        param_source = 'default_design';
    end
    
    % fc_target参数验证
    if nargin < 16 || isempty(fc_target)
        fc_target = 0;  % 设置默认值为0（不设置目标截止频率）
    end
    
    % 创建参数结构体
    design_params.opamp.W0 = W0;
    design_params.opamp.W1 = W1;
    design_params.opamp.W2 = W2;
    design_params.opamp.W3 = W3;
    design_params.opamp.W5 = W5;
    design_params.opamp.W7 = W7;
    design_params.opamp.L = 1;
    design_params.opamp.Ibias = Ibias;
    design_params.opamp.Cc = Cc;
    design_params.opamp.Rc = Rc;
    
    design_params.filter.R1 = R1;
    design_params.filter.R3 = R3;
    design_params.filter.R5 = R5;
    design_params.filter.C1 = C1;
    design_params.filter.C2 = C2;
    design_params.filter.C3 = C3;
    design_params.filter.fc_target = fc_target;
    design_params.filter.param_source = param_source;
    
    design_params.timestamp = datestr(now);
    
    % 保存到MAT文件
    save('lpf_integrated_params.mat', 'design_params');
end

%% 生成综合报告
function generate_comprehensive_report(use_optimized_params, param_source)
    % 参数说明：
    % - use_optimized_params: 布尔值，指示是否使用优化参数
    % - param_source: 字符串，指示参数来源，'noise_optimized'或'default_design'
    
    % 使用传入的参数，不强制覆盖
    if nargin < 1
        use_optimized_params = true;
    end
    
    if nargin < 2
        param_source = 'noise_optimized';
    end
    
    fid = fopen('lpf_integrated_report.txt', 'w');
    
    fprintf(fid, '低通滤波器综合设计报告\n');
    fprintf(fid, '========================\n');
    fprintf(fid, '生成时间: %s\n\n', datestr(now));
    
    % 设计概述
    fprintf(fid, '【设计概述】\n');
    fprintf(fid, '设计类型: 三阶低通滤波器\n');
    fprintf(fid, '参数来源: %s\n', param_source);
    fprintf(fid, '运放类型: 两级运算放大器\n');
    fprintf(fid, '工艺: SMIC 180nm\n\n');
    
    % 噪声优化结果
    if exist('optimal_parameters.mat', 'file')
        try
            opt_data = load('optimal_parameters.mat');
            % 验证加载的数据包含所需的字段
            if isfield(opt_data, 'min_noise') && isfield(opt_data, 'area') && isfield(opt_data, 'initial_noise')
                fprintf(fid, '【噪声优化结果】\n');
                fprintf(fid, '最小噪声: %.4e\n', opt_data.min_noise);
                fprintf(fid, '电路面积: %.2f μm²\n', opt_data.area);
                fprintf(fid, '噪声改善: %.2f%%\n\n', 100*(1 - opt_data.min_noise/opt_data.initial_noise));
            else
                fprintf(fid, '【噪声优化结果】\n');
                fprintf(fid, '加载的优化结果数据不完整\n\n');
            end
        catch ME
            fprintf(fid, '【噪声优化结果】\n');
            fprintf(fid, '无法加载优化结果数据: %s\n\n', ME.message);
        end
    else
        fprintf(fid, '【噪声优化结果】\n');
        fprintf(fid, '未找到优化结果文件\n\n');
    end
    
    % 加载设计参数
    if exist('lpf_integrated_params.mat', 'file')
        params = load('lpf_integrated_params.mat');
        
        fprintf(fid, '【运放参数】\n');
        fprintf(fid, 'W0=%.1f, W1=%.1f, W2=%.1f μm\n', ...
                params.design_params.opamp.W0, ...
                params.design_params.opamp.W1, ...
                params.design_params.opamp.W2);
        fprintf(fid, 'W3=%.2f, W5=%.1f, W7=%.1f μm\n', ...
                params.design_params.opamp.W3, ...
                params.design_params.opamp.W5, ...
                params.design_params.opamp.W7);
        fprintf(fid, 'Ibias=%.1f μA, Cc=%.2f pF, Rc=%.3f kΩ\n\n', ...
                params.design_params.opamp.Ibias, ...
                params.design_params.opamp.Cc, ...
                params.design_params.opamp.Rc);
        
        fprintf(fid, '【滤波器参数】\n');
        % 只有当fc_target不为0时才输出目标截止频率
        if params.design_params.filter.fc_target ~= 0
            fprintf(fid, '目标截止频率: %.3f MHz\n', params.design_params.filter.fc_target/1e6);
        else
            fprintf(fid, '目标截止频率: 未指定\n');
        end
        
        fprintf(fid, 'R1=%.3f, R3=%.3f, R5=%.3f kΩ\n', ...
                params.design_params.filter.R1, ...
                params.design_params.filter.R3, ...
                params.design_params.filter.R5);
        fprintf(fid, 'C1=%.3f, C2=%.3f, C3=%.3f pF\n\n', ...
                params.design_params.filter.C1, ...
                params.design_params.filter.C2, ...
                params.design_params.filter.C3);
    end
    
    % 性能指标
    fprintf(fid, '【仿真性能】\n');
    fprintf(fid, '详见仿真结果文件: LPF.lis\n');
    fprintf(fid, '频率响应图: lpf_frequency_response.png\n\n');
    
    % 文件列表
    fprintf(fid, '【输出文件】\n');
    fprintf(fid, '- LPF.sp: SPICE网表\n');
    fprintf(fid, '- LPF.lis: 仿真结果\n');
    fprintf(fid, '- lpf_integrated_params.mat: 设计参数\n');
    fprintf(fid, '- lpf_integrated_report.txt: 本报告\n');
    fprintf(fid, '- lpf_frequency_response.png: 频率响应图\n');
    
    fclose(fid);
    
    fprintf('✓ 综合报告已生成: lpf_integrated_report.txt\n');
end 