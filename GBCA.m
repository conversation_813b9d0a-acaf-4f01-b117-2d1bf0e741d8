function GBCA(W0,W1,W2,W3,W5,W7,L,VDD,VSS,Cc,CL,Ibias,Rc)
    global  pathsp %#ok<GVMIS>

       fp=fopen('GBCA.sp','w+');
       fprintf(fp,'%s\n','*CMOS OP2');
       fprintf(fp,'%s\n','.OPTIONs post=2 measfall=0 ingold=2 NOMOD accurate probe');
       fprintf(fp,'%s\n','.include ''baluninout.sp''');
       fprintf(fp,'\n');

       fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM1 net1 net3 VDD VDD p18 W=',W0,'U ' ,'L=',0.5*L,'U');
       fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM0 net3 net3 VDD VDD p18 W=',W0,'U ' ,'L=',0.5*L,'U');
       fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MPM2 VO net1 VDD VDD p18 W=',W2,'U ' ,'L=',0.18,'U');
       fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM5 I I GND GND n18 W=',W5,'U ' ,'L=',0.5*L,'U');
       fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM3 net015 I GND GND n18 W=',W3,'U ' ,'L=',0.5*L,'U');
       fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM1 net1 Vin net015 GND n18 W=',W1,'U ' ,'L=',0.5*L,'U');
       fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM0 net3 Vip net015 GND n18 W=',W1,'U ' ,'L=',0.5*L,'U');
       fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','MNM4 VO I GND GND n18 W=',W7,'U ' ,'L=',0.18,'U');
       fprintf(fp,'RR0 net1 net027 %.5fK\n',Rc);
       fprintf(fp,'CC0 net027 VO %.5fp\n',Cc);
       fprintf(fp,'CC1 VO GND %.5fp\n',CL);
   
       fprintf(fp,'%s\n','.LIB ''ms018_v1p9.lib'' TT');
       
       fprintf(fp,'%s%9.5f\n','VDD VDD 0 DC ', VDD);
       fprintf(fp,'%s%9.5f\n','VVcm Vcm 0 DC ', VDD/2);

       fprintf(fp,'%s%9.5f\n','VSS VSS 0 DC ', VSS);
       fprintf(fp,'Ibias VDD I  DC %.5fuA\n', Ibias);
       fprintf(fp,'%s\n','VVI Vi 0 DC 0 AC 1 0');
       fprintf(fp,'%s\n','X1 Vi Vcm VSS Vip Vin balunin');
       fprintf(fp,'%s\n','.TEMP 27');
       fprintf(fp,'%s\n','.OP');
       fprintf(fp,'%s\n','.AC DEC 20 1 1000MEG');
       fprintf(fp,'%s\n','.noise v(vo) vvi');
       fprintf(fp,'%s\n','.MEAS AC DCGAIN MAX VDB(VO)');
       fprintf(fp,'%s\n','.MEAS AC UGB WHEN VDB(VO)=0');
       fprintf(fp,'%s\n','.MEAS AC PHASE_AT_UGB FIND VP(VO) AT=UGB');
       fprintf(fp,'%s\n','.probe inoise onoise');
       %fprintf(fp,'%s\n','.MEAS AC PM PARAM="180+PHASE_AT_UGB"');

       fprintf(fp,'%s\n','.END');
       fclose(fp);
       
       % 检查HSPICE路径是否设置
       if isempty(pathsp)
           pathsp = 'D:\synopsys\Hspice_P-2019.06-SP1-1\WIN64\hspice.exe';
           fprintf('使用默认HSPICE路径: %s\n', pathsp);
       end
       
       % 使用pathsp变量调用HSPICE
       cmd = [pathsp ' -i GBCA.sp -o GBCA'];
       system(cmd);
end 