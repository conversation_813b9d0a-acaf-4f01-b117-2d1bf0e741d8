*CMOS OP2
.OPTIONs post=2 measfall=0 ingold=2 NOMOD accurate probe
.include 'baluninout.sp'

MPM1 net1 net3 VDD VDD p18 W= 36.40000U L=  0.50000U
MPM0 net3 net3 VDD VDD p18 W= 36.40000U L=  0.50000U
MPM2 VO net1 VDD VDD p18 W=158.70000U L=  0.18000U
MNM5 I I GND GND n18 W= 17.00000U L=  0.50000U
MNM3 net015 I GND GND n18 W= 17.50000U L=  0.50000U
MNM1 net1 Vin net015 GND n18 W= 30.20000U L=  0.50000U
MNM0 net3 Vip net015 GND n18 W= 30.20000U L=  0.50000U
MNM4 VO I GND GND n18 W= 47.80000U L=  0.18000U
RR0 net1 net027 0.12300K
CC0 net027 VO 1.00000p
CC1 VO GND 4.00000p
.LIB 'ms018_v1p9.lib' TT
VDD VDD 0 DC   1.80000
VVcm Vcm 0 DC   0.90000
VSS VSS 0 DC   0.00000
Ibias VDD I  DC 111.60000uA
VVI Vi 0 DC 0 AC 1 0
X1 Vi Vcm VSS Vip Vin balunin
.TEMP 27
.OP
.AC DEC 20 1 1000MEG
.noise v(vo) vvi
.MEAS AC DCGAIN MAX VDB(VO)
.MEAS AC UGB WHEN VDB(VO)=0
.MEAS AC PHASE_AT_UGB FIND VP(VO) AT=UGB
.probe inoise onoise
.END
