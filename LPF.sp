.title  Low Pass Filter with Three Op-Amps
.OPTIONs post=2 measfall=0 ingold=2 NOMOD accurate
.include 'baluninout.sp'

.SUBCKT op2_cursor_diff GND I VDD Vb Vin Vip Von Vop
MNM8 net6 Vb net4 GND n18 W=  2.80000U L=  1.00000U m=1
MNM7 net3 net5 net4 GND n18 W=  1.27500U L=  1.00000U m=1
MNM6 net4 I GND GND n18 W=  2.55000U L=  1.00000U m=1
MNM2 Von I GND GND n18 W= 26.90000U L=  1.00000U m=1
MNM1 net42 Vin net41 GND n18 W=  2.80000U L=  1.00000U m=1
MNM0 net1 Vip net41 GND n18 W=  2.80000U L=  1.00000U m=1
MNM3 net41 I GND GND n18 W=  2.55000U L=  1.00000U m=1
MNM4 Vop I GND GND n18 W= 26.90000U L=  1.00000U m=1
MNM5 I I GND GND n18 W=  2.50000U L=  1.00000U m=1
RR6 Vop net5 100K $[RP]
RR5 Von net5 100K $[RP]
RR1 net1 net2 0.92800K $[RP]
RR0 net42 net43 0.92800K $[RP]
CC2 Vop net5 260f $[CP]
CC3 Von net5 260f $[CP]
CC1 net2 Von 1.00000p $[CP]
CC0 net43 Vop 1.00000p $[CP]
MPM5 net6 net6 VDD VDD p18 W=  5.60000U L=  1.00000U m=1
MPM4 net3 net3 VDD VDD p18 W=  5.60000U L=  1.00000U m=1
MPM3 Von net1 VDD VDD p18 W= 12.36000U L=  1.00000U m=10
MPM1 net42 net3 VDD VDD p18 W=  5.60000U L=  1.00000U m=1
MPM0 net1 net3 VDD VDD p18 W=  5.60000U L=  1.00000U m=1
MPM2 Vop net42 VDD VDD p18 W= 12.36000U L=  1.00000U m=10
.ENDS

************************************************************************
* Library Name: LPF_design_SMIC
* Cell Name:    LPFforHspice
* View Name:    schematic
************************************************************************

.SUBCKT LPFforHspice AVDD AVSS Ibias0 Ibias1 Ibias2 Vb Vin Vip Von Vop
RR56 net6 Vop 116.89244K $[RP]
RR52 net5 Von 116.89244K $[RP]
RR8 net4 net5 116.89244K $[RP]
RR9 net3 net6 116.89244K $[RP]
RR55 net1 Von 129.59092K $[RP]
RR57 Vop net2 129.59092K $[RP]
RR5 net13 net2 129.59092K $[RP]
RR4 net12 net1 129.59092K $[RP]
RR3 net10 net12 76.68238K $[RP]
RR2 net11 net13 76.68238K $[RP]
RR7 net10 net4 76.68238K $[RP]
RR6 net11 net3 76.68238K $[RP]
RR1 Vin net10 38.34119K $[RP]
RR0 Vip net11 38.34119K $[RP]
CC19 net6 Vop 2.61837p $[CP]
CC18 net5 Von 2.61837p $[CP]
CC2 net2 net4 4.72359p $[CP]
CC3 net1 net3 4.72359p $[CP]
CC1 net10 net12 3.99136p $[CP]
CC0 net11 net13 3.99136p $[CP]
XI0 AVSS Ibias0 AVDD Vb net10 net11 net13 net12 / op2_cursor_diff
XI1 AVSS Ibias1 AVDD Vb net2 net1 net3 net4 / op2_cursor_diff
XI2 AVSS Ibias2 AVDD Vb net6 net5 Von Vop / op2_cursor_diff
.ENDS

X1 AVDD AVSS Ibias0 Ibias1 Ibias2 Vcm Vin Vip Von Vop LPFforHspice
X2 Vi Vcm AVSS Vip Vin balunin
X3 Vop Von AVSS Vo balunout

VAVDD AVDD 0 DC   1.80000
VAVSS AVSS 0 DC   0.00000
VVcm Vcm 0 DC   0.90000
IIbias0 AVDD Ibias0 DC 8.60000uA
IIbias1 AVDD Ibias1 DC 8.60000uA
IIbias2 AVDD Ibias2 DC 8.60000uA
VVI Vi 0 DC 0 AC 1 0
.TEMP 27
.OP
.AC DEC 100 1 100MEG
.MEAS AC DCGAIN MAX VDB(Vo)
.MEAS AC FC3dB WHEN VDB(Vo)="DCGAIN-3"
.LIB 'ms018_v1p9.lib' TT
.END
