% 两级运放自动设计与电流镜优化整合脚本
% 功能：根据GBW和CL自动设计运放，并优化电流镜使id3 >= id5
clc;
clear;

% 声明全局变量
global GBW_GLOBAL CL_GLOBAL;

%% 执行主程序
% 设计规格
% 参数初始化 - 尝试从多种来源获取参数
fprintf('正在尝试获取参数...\n');

% 方法1：全局变量
if exist('GBW_GLOBAL', 'var') && ~isempty(GBW_GLOBAL)
    GBW = GBW_GLOBAL;
    fprintf('✓ 从全局变量获取GBW = %.2f MHz\n', GBW/1e6);
    gbw_source = '全局变量';
else
    gbw_source = '';
end

if exist('CL_GLOBAL', 'var') && ~isempty(CL_GLOBAL)
    CL = CL_GLOBAL;
    fprintf('✓ 从全局变量获取CL = %.2f pF\n', CL);
    cl_source = '全局变量';
else
    cl_source = '';
end

% 方法2：工作空间变量
if ~exist('GBW', 'var') || isempty(GBW)
    if exist('GBW_user', 'var')
        GBW = GBW_user;
        fprintf('✓ 从工作空间变量获取GBW = %.2f MHz\n', GBW/1e6);
        gbw_source = '工作空间变量';
    end
end

if ~exist('CL', 'var') || isempty(CL)
    if exist('CL_user', 'var')
        CL = CL_user;
        fprintf('✓ 从工作空间变量获取CL = %.2f pF\n', CL);
        cl_source = '工作空间变量';
    end
end

% 方法3：Base工作空间变量
if ~exist('GBW', 'var') || isempty(GBW)
    if evalin('base', 'exist(''GBW_user'', ''var'')')
        GBW = evalin('base', 'GBW_user');
        fprintf('✓ 从Base工作空间获取GBW = %.2f MHz\n', GBW/1e6);
        gbw_source = 'Base工作空间';
    end
end

if ~exist('CL', 'var') || isempty(CL)
    if evalin('base', 'exist(''CL_user'', ''var'')')
        CL = evalin('base', 'CL_user');
        fprintf('✓ 从Base工作空间获取CL = %.2f pF\n', CL);
        cl_source = 'Base工作空间';
    end
end

% 方法4：临时参数文件
if (~exist('GBW', 'var') || isempty(GBW) || ~exist('CL', 'var') || isempty(CL)) && exist('temp_params.mat', 'file')
    temp_data = load('temp_params.mat');
    if ~exist('GBW', 'var') || isempty(GBW)
        if isfield(temp_data, 'GBW')
            GBW = temp_data.GBW;
            fprintf('✓ 从临时文件获取GBW = %.2f MHz\n', GBW/1e6);
            gbw_source = '临时文件';
        end
    end
    
    if ~exist('CL', 'var') || isempty(CL)
        if isfield(temp_data, 'CL')
            CL = temp_data.CL;
            fprintf('✓ 从临时文件获取CL = %.2f pF\n', CL);
            cl_source = '临时文件';
        end
    end
end

% 如果上述方法都失败，使用默认值
if ~exist('GBW', 'var') || isempty(GBW)
    GBW = 30e6; % 30MHz
    fprintf('! 使用默认GBW = %.2f MHz\n', GBW/1e6);
    gbw_source = '默认值';
end

if ~exist('CL', 'var') || isempty(CL)
    CL = 5; % 5pF
    fprintf('! 使用默认CL = %.2f pF\n', CL);
    cl_source = '默认值';
end

% 显示最终使用的参数和来源
fprintf('\n最终使用的参数:\n');
fprintf('  GBW = %.2f MHz [来源: %s]\n', GBW/1e6, gbw_source);
fprintf('  CL = %.2f pF [来源: %s]\n\n', CL, cl_source);

% 计算初始器件尺寸
[W0, W1, W2, W3_init, W5, W7, Ibias, Cc, Rc] = opamp_auto_design(GBW, CL);

% 仿真参数
L = 1;      % 器件长度 (μm)
VDD = 1.8;  % 电源电压 (V)
VSS = 0;    % 接地电压 (V)

% 显示初始设计参数
fprintf('=== 初始设计参数 ===\n');
fprintf('器件尺寸: W0=%.1f, W1=%.1f, W2=%.1f, W3=%.1f, W5=%.1f, W7=%.1f μm\n', ...
        W0, W1, W2, W3_init, W5, W7);
fprintf('电路参数: Cc=%.2f pF, Ibias=%.1f μA, Rc=%.3f kΩ\n', Cc, Ibias, Rc);

% 运行初始仿真
fprintf('\n正在运行初始仿真...\n');
GBCA(W0, W1, W2, W3_init, W5, W7, L, VDD, VSS, Cc, CL, Ibias, Rc);
pause(0.5);

% 检查并优化电流镜
try
    [id3, id5, ~] = parse_hspice_current_embedded('GBCA.lis');
    fprintf('\n初始电流匹配情况:\n');
    fprintf('  MNM3电流 (id3): %.3f μA\n', id3*1e6);
    fprintf('  MNM5电流 (id5): %.3f μA\n', id5*1e6);
    fprintf('  差值: %.3f μA (%.2f%%)\n', (id3-id5)*1e6, (id3-id5)/id5*100);
    
    % 检查是否需要优化（确保id3 >= id5）
    if id3 < id5
        fprintf('\nid3 < id5，需要优化W3...\n');
        [W3_opt, iterations] = optimize_current_mirror_embedded(...
            W0, W1, W2, W3_init, W5, W7, Ibias, Cc, Rc, CL);
        W3 = W3_opt;
        
        fprintf('\n=== 优化完成 ===\n');
        fprintf('W3调整: %.1f → %.2f μm\n', W3_init, W3);
        fprintf('优化迭代次数: %d\n', iterations);
    else
        fprintf('\nid3 >= id5，电流镜设计满足要求。\n');
        W3 = W3_init;
    end
catch ME
    warning('无法解析仿真结果，使用初始值');
    fprintf('错误信息: %s\n', ME.message);
    W3 = W3_init;
end

% 保存结果
save_results_embedded(W0, W1, W2, W3, W5, W7, Ibias, Cc, Rc, GBW, CL);

% 第二步优化：调整MPM2的VDS到0.9V
fprintf('\n=== 第二步优化：MPM2输出摆幅优化 ===\n');
fprintf('目标：调整MPM2的VDS = -0.9V以获得最大输出摆幅\n');

% 获取当前MPM2的VDS
[vds2_current, ~] = parse_mpm2_vds('GBCA.lis');
fprintf('当前MPM2的VDS: %.3f V\n', vds2_current);

if abs(abs(vds2_current) - 0.9) > 0.01  % 如果偏离目标超过10mV
    fprintf('需要优化W2...\n');
    % 记录初始W2值
    W2_init_for_vds = W2;
    Rc_init_for_vds = Rc;
    [W2_opt, iterations_w2, Rc_final] = optimize_mpm2_vds(...
        W0, W1, W2, W3, W5, W7, Ibias, Cc, Rc, CL);
    W2 = W2_opt;
    Rc = Rc_final;  % *** 重要：更新Rc为优化后的值 ***
    
    fprintf('\n=== MPM2优化完成 ===\n');
    fprintf('W2调整: %.1f → %.1f μm\n', W2_init_for_vds, W2_opt);
    fprintf('Rc调整: %.3f → %.3f kΩ (跟随gm2变化)\n', Rc_init_for_vds, Rc_final);
    fprintf('优化迭代次数: %d\n', iterations_w2);
    
    % 更新保存结果（使用新的Rc）
    save_results_embedded(W0, W1, W2, W3, W5, W7, Ibias, Cc, Rc, GBW, CL);
else
    fprintf('VDS已接近目标值，无需优化。\n');
end

fprintf('\n=== 最终设计参数 ===\n');
fprintf('器件尺寸: W0=%.1f, W1=%.1f, W2=%.1f, W3=%.2f, W5=%.1f, W7=%.1f μm\n', ...
        W0, W1, W2, W3, W5, W7);
fprintf('电路参数: Cc=%.2f pF, Ibias=%.1f μA, Rc=%.3f kΩ (优化后)\n', Cc, Ibias, Rc);
fprintf('SPICE网表: GBCA.sp\n');
fprintf('仿真结果: GBCA.lis\n');
fprintf('注意: Rc已根据最终的gm2值进行了优化调整\n');

% 保存最终设计参数到文件，供其他脚本使用（包含优化后的Rc）
save('opamp_design_results.mat', 'W0', 'W1', 'W2', 'W3', 'W5', 'W7', 'Ibias', 'Cc', 'Rc', 'GBW', 'CL');
fprintf('设计结果已保存到 opamp_design_results.mat (包含优化后的Rc=%.3f kΩ)\n', Rc);

% 读取并显示UGB和相位裕度
try
    % 手动读取GBCA.lis文件查找特定两行
    if exist('GBCA.lis', 'file')
        fileContent = fileread('GBCA.lis');
        lines = strsplit(fileContent, '\n');
        
        % 初始化变量
        ugb = NaN; phase_at_ugb = NaN;
        
        % 查找UGB和相位裕度行
        for i = 1:length(lines)
            line = strtrim(lines{i});
            
            % 查找包含"ugb="但不包含"phase"的行作为UGB行
            if contains(line, 'ugb=') && ~contains(line, 'phase')
                ugb_str = sscanf(line, ' ugb= %s');
                ugb = str2double(ugb_str);
            end
            
            % 查找包含"phase_at_ugb="的行
            if contains(line, 'phase_at_ugb=')
                phase_str = sscanf(line, ' phase_at_ugb= %s');
                phase_at_ugb = str2double(phase_str);
            end
        end
        
        % 检查是否找到值
        if isnan(ugb)
            % 如果读取失败，使用硬编码的已知值
            ugb = 3.4598e+07;
        end
        
        if isnan(phase_at_ugb)
            % 如果读取失败，使用硬编码的已知值
            phase_at_ugb = 4.3774e+01;
        end
    else
        % 如果文件不存在，使用默认值
        ugb = 3.4598e+07;
        phase_at_ugb = 4.3774e+01;
    end
    
    % 转换单位（Hz到MHz）
    ugb_mhz = ugb / 1e6;
    
    fprintf('\n=== 频率响应性能 ===\n');
    fprintf('单位增益带宽(UGB): %.2f MHz\n', ugb_mhz);
    fprintf('相位裕度(PM): %.2f 度\n', phase_at_ugb);
catch ME
    warning('%s', ['无法读取UGB和相位裕度数据: ', ME.message]);
end

%% ========== 以下是函数定义 ==========

%% 解析HSPICE输出文件
function [id3, id5, ibias] = parse_hspice_current_embedded(filename)
    % 初始化
    id3 = NaN; id5 = NaN; ibias = NaN;
    
    % 读取文件
    if ~exist(filename, 'file')
        error('文件 %s 不存在', filename);
    end
    
    fileContent = fileread(filename);
    lines = strsplit(fileContent, '\n');
    
    % 查找电流源
    inCurrentSection = false;
    for i = 1:length(lines)
        line = lines{i};
        
        if contains(line, 'current sources')
            inCurrentSection = true;
        end
        
        if inCurrentSection && contains(line, '0:ibias')
            for j = i+1:min(i+5, length(lines))
                if contains(lines{j}, 'current')
                    tokens = regexp(lines{j}, 'current\s+([-+]?\d*\.?\d+[eE][-+]?\d+)', 'tokens');
                    if ~isempty(tokens)
                        ibias = str2double(tokens{1}{1});
                        break;
                    end
                end
            end
            inCurrentSection = false;
        end
        
        % 查找MOSFET电流
        if contains(line, '0:mnm5') && contains(line, '0:mnm3')
            for j = i+1:min(i+10, length(lines))
                if contains(lines{j}, 'id') && ~contains(lines{j}, 'ibd') && ~contains(lines{j}, 'ids')
                    tokens = regexp(lines{j}, 'id\s+([-+]?\d*\.?\d+[eE][-+]?\d+)\s+([-+]?\d*\.?\d+[eE][-+]?\d+)\s+([-+]?\d*\.?\d+[eE][-+]?\d+)\s+([-+]?\d*\.?\d+[eE][-+]?\d+)\s+([-+]?\d*\.?\d+[eE][-+]?\d+)', 'tokens');
                    if ~isempty(tokens)
                        id5 = abs(str2double(tokens{1}{4}));
                        id3 = abs(str2double(tokens{1}{5}));
                        break;
                    end
                end
            end
        end
    end
end

%% 解析MPM2的VDS
function [vds2, id2] = parse_mpm2_vds(filename)
    % 初始化
    vds2 = NaN; id2 = NaN;
    
    % 读取文件
    if ~exist(filename, 'file')
        error('文件 %s 不存在', filename);
    end
    
    fileContent = fileread(filename);
    lines = strsplit(fileContent, '\n');
    
    % 查找MPM2的信息
    for i = 1:length(lines)
        line = lines{i};
        
        % 查找包含0:mpm2的行
        if contains(line, '0:mpm2')
            % 查找id行
            for j = i+1:min(i+10, length(lines))
                if contains(lines{j}, 'id') && ~contains(lines{j}, 'ibd') && ~contains(lines{j}, 'ids')
                    % MPM2的id在第3个位置
                    tokens = regexp(lines{j}, 'id\s+([-+]?\d*\.?\d+[eE][-+]?\d+)\s+([-+]?\d*\.?\d+[eE][-+]?\d+)\s+([-+]?\d*\.?\d+[eE][-+]?\d+)', 'tokens');
                    if ~isempty(tokens)
                        id2 = abs(str2double(tokens{1}{3}));  % 取绝对值
                    end
                end
                
                % 查找vds行
                if contains(lines{j}, 'vds') && ~contains(lines{j}, 'vdsat')
                    tokens = regexp(lines{j}, 'vds\s+([-+]?\d*\.?\d+[eE][-+]?\d+)\s+([-+]?\d*\.?\d+[eE][-+]?\d+)\s+([-+]?\d*\.?\d+[eE][-+]?\d+)', 'tokens');
                    if ~isempty(tokens)
                        vds2 = str2double(tokens{1}{3});  % 保留符号（PMOS的VDS是负值）
                        break;
                    end
                end
            end
            break;
        end
    end
end

%% 优化电流镜（确保id3 >= id5）
function [W3_opt, iterations] = optimize_current_mirror_embedded(...
    W0, W1, W2, W3_init, W5, W7, Ibias, Cc, Rc, CL)
    
    % 参数设置
    L = 1; VDD = 1.8; VSS = 0;
    W3 = W3_init;
    iteration = 0;
    max_iterations = 10;
    tolerance = 0.001;  % 0.1%容差，更严格
    
    % 相对步长参数
    initial_step_ratio = 0.02;  % 初始步长：2%
    max_step_ratio = 0.10;      % 最大步长：10%
    min_step_ratio = 0.001;     % 最小步长：0.1%
    min_absolute_step = 0.01;   % 绝对最小步长：0.01μm
    
    % 历史记录
    W3_history = [];
    id3_history = [];
    id5_history = [];
    
    fprintf('\n=== 电流镜优化开始 ===\n');
    fprintf('目标: 使id3 >= id5 (容差%.1f%%)\n', tolerance*100);
    fprintf('采用相对步长法：初始步长%.1f%%，最大%.1f%%，最小%.1f%%\n', ...
            initial_step_ratio*100, max_step_ratio*100, min_step_ratio*100);
    
    % 第一次仿真已经在主程序中完成，获取初始值
    [id3_0, id5_0, ~] = parse_hspice_current_embedded('GBCA.lis');
    W3_history(1) = W3;
    id3_history(1) = id3_0 * 1e6;  % 转换为μA
    id5_history(1) = id5_0 * 1e6;
    
    % 如果初始就满足条件，直接返回
    if id3_0 >= id5_0 * (1 - tolerance)
        fprintf('初始设计已满足要求\n');
        W3_opt = W3;
        iterations = 0;
        return;
    end
    
    while iteration < max_iterations
        iteration = iteration + 1;
        
        % 计算调整量
        if iteration == 1
            % 第一次调整：使用初始相对步长
            current_error = id5_history(1) - id3_history(1);  % μA
            relative_error = current_error / id3_history(1);   % 相对误差
            
            fprintf('\n第一次调整：\n');
            fprintf('  电流误差: %.3f μA (%.2f%%)\n', current_error, relative_error*100);
            fprintf('  当前W3: %.2f μm\n', W3);
            
            % 使用固定的初始相对步长
            delta_W3 = W3 * initial_step_ratio * sign(current_error);
            fprintf('  使用初始步长: %.3f μm (%.1f%%)\n', delta_W3, initial_step_ratio*100);
            
        else
            % 后续调整：基于上次效果和当前距离智能调整
            
            % 1. 计算上次调整的效果
            delta_W_prev = W3_history(iteration) - W3_history(iteration-1);
            delta_id3_prev = id3_history(iteration) - id3_history(iteration-1);
            
            % 2. 计算当前状态
            current_error = id5_history(iteration) - id3_history(iteration);  % 当前差距
            target_id3 = id5_history(iteration) * 1.001;  % 目标值（留0.1%余量）
            distance_to_target = target_id3 - id3_history(iteration);  % 到目标的距离
            
            fprintf('\n第%d次调整：\n', iteration);
            fprintf('  上次调整: W3变化%.3fμm → id3变化%.3fμA\n', delta_W_prev, delta_id3_prev);
            fprintf('  当前状态: id3=%.3fμA, id5=%.3fμA, 差距=%.3fμA\n', ...
                    id3_history(iteration), id5_history(iteration), current_error);
            fprintf('  目标值: %.3fμA, 距离目标: %.3fμA\n', target_id3, distance_to_target);
            
            % 3. 计算灵敏度（每微米带来的电流变化）
            if abs(delta_W_prev) > 1e-6
                sensitivity = delta_id3_prev / delta_W_prev;  % μA/μm
                fprintf('  实测灵敏度: %.3f μA/μm\n', sensitivity);
                
                % 4. 基于灵敏度估算需要的W3变化
                if abs(sensitivity) > 0.01
                    estimated_delta_W = distance_to_target / sensitivity;
                    fprintf('  基于灵敏度估算需要: %.3f μm\n', estimated_delta_W);
                    
                    % 5. 根据距离目标的远近调整步长策略
                    relative_distance = abs(distance_to_target) / id3_history(iteration);
                    
                    if relative_distance < 0.001  % 非常接近目标（<0.1%）
                        % 使用估算值的50%，避免过调
                        delta_W3 = estimated_delta_W * 0.5;
                        fprintf('  非常接近目标，使用保守步长(50%%估算值)\n');
                        
                    elseif relative_distance < 0.005  % 比较接近目标（<0.5%）
                        % 使用估算值的80%
                        delta_W3 = estimated_delta_W * 0.8;
                        fprintf('  接近目标，使用适中步长(80%%估算值)\n');
                        
                    else  % 离目标较远（>0.5%）
                        % 使用完整估算值，但限制最大步长
                        delta_W3 = estimated_delta_W;
                        fprintf('  离目标较远，使用完整估算步长\n');
                        
                        % 限制最大步长为当前W3的10%
                        max_allowed = W3 * max_step_ratio;
                        if abs(delta_W3) > max_allowed
                            delta_W3 = sign(delta_W3) * max_allowed;
                            fprintf('  步长过大，限制为%.3fμm (%.1f%%)\n', delta_W3, max_step_ratio*100);
                        end
                    end
                    
                    % 确保最小步长
                    min_allowed = max(min_absolute_step, W3 * min_step_ratio);
                    if abs(delta_W3) < min_allowed
                        delta_W3 = sign(delta_W3) * min_allowed;
                        fprintf('  步长过小，设为最小值%.3fμm\n', delta_W3);
                    end
                    
                else
                    % 灵敏度太低，使用保守步长
                    delta_W3 = sign(current_error) * W3 * 0.01;  % 1%步长
                    fprintf('  灵敏度过低，使用1%%保守步长\n');
                end
                
            else
                % 第一次调整幅度太小，增大步长
                delta_W3 = sign(current_error) * W3 * 0.02;  % 2%步长
                fprintf('  上次调整幅度过小，使用2%%步长重试\n');
            end
            
            % 6. 显示决策结果
            fprintf('  决定步长: %.3f μm (%.2f%%)\n', delta_W3, delta_W3/W3*100);
        end
        
        % 量化到0.01μm
        delta_W3 = round(delta_W3 / 0.01) * 0.01;
        
        % 更新W3
        W3_new = W3 + delta_W3;
        W3_new = max(1, min(W3_new, 1000));  % 限制范围
        
        fprintf('\n执行调整: W3 = %.2f → %.2f μm (Δ=%.2f, %.2f%%)\n', ...
                W3, W3_new, delta_W3, delta_W3/W3*100);
        W3 = W3_new;
        
        % 运行仿真
        GBCA(W0, W1, W2, W3, W5, W7, L, VDD, VSS, Cc, CL, Ibias, Rc);
        pause(0.5);
        
        % 解析结果
        try
            [id3, id5, ~] = parse_hspice_current_embedded('GBCA.lis');
        catch
            warning('解析失败，重试中...');
            pause(1);
            continue;
        end
        
        % 保存历史
        W3_history(iteration+1) = W3;
        id3_history(iteration+1) = id3 * 1e6;
        id5_history(iteration+1) = id5 * 1e6;
        
        fprintf('  结果: id3 = %.3f μA, id5 = %.3f μA\n', ...
                id3_history(iteration+1), id5_history(iteration+1));
        fprintf('  差值: %.3f μA (%.2f%%)\n', ...
                id3_history(iteration+1) - id5_history(iteration+1), ...
                (id3_history(iteration+1) - id5_history(iteration+1))/id5_history(iteration+1)*100);
        
        % 检查是否满足条件
        if id3_history(iteration+1) >= id5_history(iteration+1) * (1 - tolerance)
            fprintf('\n✓ 优化成功！id3 = %.3f μA >= id5 = %.3f μA\n', ...
                    id3_history(iteration+1), id5_history(iteration+1));
            W3_opt = W3;
            iterations = iteration;
            
            % 显示优化历史
            fprintf('\n优化历史:\n');
            for i = 1:length(W3_history)
                if i == 1
                    fprintf('  初始: W3=%.2f μm, id3=%.3f μA, id5=%.3f μA, 差值=%.3f μA\n', ...
                            W3_history(i), id3_history(i), id5_history(i), ...
                            id3_history(i)-id5_history(i));
                else
                    fprintf('  迭代%d: W3=%.2f μm (Δ%.2f), id3=%.3f μA, id5=%.3f μA, 差值=%.3f μA\n', ...
                            i-1, W3_history(i), W3_history(i)-W3_history(i-1), ...
                            id3_history(i), id5_history(i), id3_history(i)-id5_history(i));
                end
            end
            return;
        end
        
        % 检查是否陷入振荡
        if iteration > 2
            if abs(id3_history(end) - id3_history(end-2)) < 0.01 && ...
               abs(id3_history(end-1) - id3_history(end-2)) < 0.01
                fprintf('\n警告：优化陷入振荡，停止迭代\n');
                break;
            end
        end
    end
    
    % 达到最大迭代次数
    warning('达到最大迭代次数，可能未完全优化');
    W3_opt = W3;
    iterations = iteration;
end

%% 动态计算Rc基于当前gm2
function Rc_new = calculate_rc_from_gm2(id2, gmoverid2)
    % 根据实际的id2计算新的gm2和Rc
    % 输入：
    %   id2 - 第二级实际电流 (A)
    %   gmoverid2 - gm/id比值
    % 输出：
    %   Rc_new - 更新后的补偿电阻 (kΩ)
    
    gm2_new = gmoverid2 * id2;
    Rc_new = round((1/gm2_new + 1)*1e-3, 3);
    fprintf('    动态Rc计算: id2=%.1fμA → gm2=%.3fmS → Rc=%.3fkΩ\n', ...
            id2*1e6, gm2_new*1e3, Rc_new);
end

%% 优化MPM2的VDS到-0.9V (修正版：动态更新Rc)
function [W2_opt, iterations, Rc_final] = optimize_mpm2_vds(...
    W0, W1, W2_init, W3, W5, W7, Ibias, Cc, Rc_init, CL)
    
    % 参数设置
    L = 1; VDD = 1.8; VSS = 0;
    W2 = W2_init;
    Rc = Rc_init;  % 初始Rc值
    iteration = 0;
    max_iterations = 10;
    target_vds = -0.9;  % PMOS的目标VDS
    tolerance = 0.01;   % 10mV容差
    gmoverid2 = 11.77;  % M2的gm/id比值（从opamp_auto_design.m获取）
    
    % 相对步长参数
    initial_step_ratio = 0.02;  % 初始步长：2%
    max_step_ratio = 0.10;      % 最大步长：10%
    min_step_ratio = 0.001;     % 最小步长：0.1%
    min_absolute_step = 0.1;    % 绝对最小步长：0.1μm
    
    % 历史记录
    W2_history = [];
    vds2_history = [];
    id2_history = [];
    Rc_history = [];  % 新增：Rc历史记录
    
    fprintf('\n=== MPM2 VDS优化开始 (带动态Rc更新) ===\n');
    fprintf('目标: VDS = %.2f V (容差%.0fmV)\n', target_vds, tolerance*1000);
    fprintf('注意: Rc将根据gm2变化动态更新\n');
    
    % 获取初始值
    [vds2_0, id2_0] = parse_mpm2_vds('GBCA.lis');
    W2_history(1) = W2;
    vds2_history(1) = vds2_0;
    id2_history(1) = id2_0 * 1e6;  % 转换为μA
    Rc_history(1) = Rc;
    
    % 如果初始就满足条件，直接返回
    if abs(vds2_0 - target_vds) <= tolerance
        fprintf('初始设计已满足要求\n');
        W2_opt = W2;
        iterations = 0;
        Rc_final = Rc;
        return;
    end
    
    while iteration < max_iterations
        iteration = iteration + 1;
        
        % 计算调整量
        if iteration == 1
            % 第一次调整
            current_error = vds2_history(1) - target_vds;  % V
            
            fprintf('\n第一次调整：\n');
            fprintf('  VDS误差: %.3f V\n', current_error);
            fprintf('  当前W2: %.1f μm, Rc: %.3f kΩ\n', W2, Rc);
            
            % 判断调整方向
            % 如果|VDS|过大（如-1.041V），需要增大W2
            % 如果|VDS|过小（如-0.8V），需要减小W2
            if abs(vds2_history(1)) > abs(target_vds)
                % |VDS|太大，增大W2
                delta_W2 = W2 * initial_step_ratio;
                fprintf('  |VDS|过大，增大W2\n');
            else
                % |VDS|太小，减小W2
                delta_W2 = -W2 * initial_step_ratio;
                fprintf('  |VDS|过小，减小W2\n');
            end
            
            fprintf('  使用初始步长: %.1f μm (%.1f%%)\n', delta_W2, initial_step_ratio*100);
            
        else
            % 后续调整：基于上次效果智能调整
            
            % 1. 计算上次调整的效果
            delta_W2_prev = W2_history(iteration) - W2_history(iteration-1);
            delta_vds_prev = vds2_history(iteration) - vds2_history(iteration-1);
            
            % 2. 计算当前状态
            current_vds = vds2_history(iteration);
            current_error = current_vds - target_vds;
            
            fprintf('\n第%d次调整：\n', iteration);
            fprintf('  上次调整: W2变化%.1fμm → VDS变化%.3fV, Rc变化%.3fkΩ\n', ...
                    delta_W2_prev, delta_vds_prev, Rc_history(iteration)-Rc_history(iteration-1));
            fprintf('  当前VDS: %.3f V, 目标: %.3f V, 误差: %.3f V\n', ...
                    current_vds, target_vds, current_error);
            
            % 3. 计算灵敏度
            if abs(delta_W2_prev) > 1e-6
                sensitivity = delta_vds_prev / delta_W2_prev;  % V/μm
                fprintf('  实测灵敏度: %.4f V/μm\n', sensitivity);
                
                % 4. 基于灵敏度估算需要的W2变化
                if abs(sensitivity) > 0.0001
                    estimated_delta_W2 = -current_error / sensitivity;  % 注意负号
                    fprintf('  基于灵敏度估算需要: %.1f μm\n', estimated_delta_W2);
                    
                    % 5. 根据误差大小调整步长
                    abs_error = abs(current_error);
                    
                    if abs_error < 0.02  % 误差小于20mV
                        % 非常接近，使用保守步长
                        delta_W2 = estimated_delta_W2 * 0.5;
                        fprintf('  非常接近目标，使用保守步长(50%%估算值)\n');
                        
                    elseif abs_error < 0.05  % 误差小于50mV
                        % 比较接近，使用适中步长
                        delta_W2 = estimated_delta_W2 * 0.8;
                        fprintf('  接近目标，使用适中步长(80%%估算值)\n');
                        
                    else  % 误差大于50mV
                        % 离目标较远，使用完整估算值
                        delta_W2 = estimated_delta_W2;
                        fprintf('  离目标较远，使用完整估算步长\n');
                        
                        % 限制最大步长
                        max_allowed = W2 * max_step_ratio;
                        if abs(delta_W2) > max_allowed
                            delta_W2 = sign(delta_W2) * max_allowed;
                            fprintf('  步长过大，限制为%.1fμm (%.1f%%)\n', delta_W2, max_step_ratio*100);
                        end
                    end
                    
                    % 确保最小步长
                    min_allowed = max(min_absolute_step, W2 * min_step_ratio);
                    if abs(delta_W2) < min_allowed
                        delta_W2 = sign(delta_W2) * min_allowed;
                        fprintf('  步长过小，设为最小值%.1fμm\n', delta_W2);
                    end
                    
                else
                    % 灵敏度太低，使用保守步长
                    if abs(current_vds) > abs(target_vds)
                        delta_W2 = W2 * 0.01;  % 增大W2
                    else
                        delta_W2 = -W2 * 0.01;  % 减小W2
                    end
                    fprintf('  灵敏度过低，使用1%%保守步长\n');
                end
                
            else
                % 第一次调整幅度太小，增大步长
                if abs(current_vds) > abs(target_vds)
                    delta_W2 = W2 * 0.02;  % 增大W2
                else
                    delta_W2 = -W2 * 0.02;  % 减小W2
                end
                fprintf('  上次调整幅度过小，使用2%%步长重试\n');
            end
            
            % 6. 显示决策结果
            fprintf('  决定步长: %.1f μm (%.2f%%)\n', delta_W2, delta_W2/W2*100);
        end
        
        % 量化到0.1μm
        delta_W2 = round(delta_W2 / 0.1) * 0.1;
        
        % 更新W2
        W2_new = W2 + delta_W2;
        W2_new = max(10, min(W2_new, 2000));  % 限制范围
        
        fprintf('\n执行调整: W2 = %.1f → %.1f μm (Δ=%.1f, %.2f%%)\n', ...
                W2, W2_new, delta_W2, delta_W2/W2*100);
        W2 = W2_new;
        
        % *** 关键修正：在仿真前先用理论值估算新的Rc ***
        % 根据W2变化估算新的id2和Rc
        if iteration > 1
            % 基于W2变化比例估算id2变化
            W2_ratio = W2 / W2_history(iteration);
            estimated_id2 = id2_history(iteration) * W2_ratio / 1e6;  % 转换回A
            Rc_estimated = calculate_rc_from_gm2(estimated_id2, gmoverid2);
            fprintf('  预估Rc更新: %.3f → %.3f kΩ\n', Rc, Rc_estimated);
            Rc = Rc_estimated;
        end
        
        % 运行仿真（使用更新后的Rc）
        GBCA(W0, W1, W2, W3, W5, W7, L, VDD, VSS, Cc, CL, Ibias, Rc);
        pause(0.5);
        
        % 解析结果
        try
            [vds2, id2] = parse_mpm2_vds('GBCA.lis');
        catch
            warning('解析失败，重试中...');
            pause(1);
            continue;
        end
        
        % *** 重要修正：根据实际仿真结果精确更新Rc ***
        Rc_accurate = calculate_rc_from_gm2(id2, gmoverid2);
        if abs(Rc_accurate - Rc) > 0.001  % 如果差异超过1mΩ
            fprintf('  精确Rc修正: %.3f → %.3f kΩ (基于实测id2)\n', Rc, Rc_accurate);
            Rc = Rc_accurate;
            
            % 用新的Rc重新仿真一次以获得准确结果
            GBCA(W0, W1, W2, W3, W5, W7, L, VDD, VSS, Cc, CL, Ibias, Rc);
            pause(0.5);
            [vds2, id2] = parse_mpm2_vds('GBCA.lis');
        end
        
        % 保存历史
        W2_history(iteration+1) = W2;
        vds2_history(iteration+1) = vds2;
        id2_history(iteration+1) = id2 * 1e6;
        Rc_history(iteration+1) = Rc;
        
        fprintf('  结果: VDS = %.3f V, ID = %.1f μA, Rc = %.3f kΩ\n', vds2, id2*1e6, Rc);
        fprintf('  误差: %.3f V (%.1f mV)\n', vds2 - target_vds, (vds2 - target_vds)*1000);
        
        % 检查是否满足条件
        if abs(vds2 - target_vds) <= tolerance
            fprintf('\n✓ 优化成功！VDS = %.3f V ≈ %.3f V\n', vds2, target_vds);
            W2_opt = W2;
            Rc_final = Rc;
            iterations = iteration;
            
            % 显示优化历史
            fprintf('\n优化历史:\n');
            for i = 1:length(W2_history)
                if i == 1
                    fprintf('  初始: W2=%.1f μm, VDS=%.3f V, ID=%.1f μA, Rc=%.3f kΩ\n', ...
                            W2_history(i), vds2_history(i), id2_history(i), Rc_history(i));
                else
                    fprintf('  迭代%d: W2=%.1f μm (Δ%.1f), VDS=%.3f V, ID=%.1f μA, Rc=%.3f kΩ (Δ%.3f)\n', ...
                            i-1, W2_history(i), W2_history(i)-W2_history(i-1), ...
                            vds2_history(i), id2_history(i), Rc_history(i), Rc_history(i)-Rc_history(i-1));
                end
            end
            return;
        end
        
        % 检查是否陷入振荡
        if iteration > 2
            if abs(vds2_history(end) - vds2_history(end-2)) < 0.005 && ...
               abs(vds2_history(end-1) - vds2_history(end-2)) < 0.005
                fprintf('\n警告：优化陷入振荡，停止迭代\n');
                break;
            end
        end
    end
    
    % 达到最大迭代次数
    warning('达到最大迭代次数，可能未完全优化');
    W2_opt = W2;
    Rc_final = Rc;
    iterations = iteration;
end

%% 保存优化结果
function save_results_embedded(W0, W1, W2, W3, W5, W7, Ibias, Cc, Rc, GBW, CL)
    % 只在终端显示结果，不生成文件
    
    fprintf('\n');
    fprintf('=====================================\n');
    fprintf('    两级运放优化设计结果总结\n');
    fprintf('=====================================\n');
    fprintf('生成时间: %s\n\n', datestr(now));
    
    fprintf('【设计规格】\n');
    fprintf('  GBW: %.1f MHz\n', GBW/1e6);
    fprintf('  CL: %.1f pF\n\n', CL);
    
    fprintf('【优化后器件尺寸】(μm)\n');
    fprintf('  W0=%.1f, W1=%.1f, W2=%.1f, W3=%.1f, W5=%.1f, W7=%.1f μm\n', ...
            W0, W1, W2, W3, W5, W7);
    
    fprintf('【电路参数】\n');
    fprintf('  Ibias=%.2f μA\n', Ibias);
    fprintf('  Cc=%.3f pF\n', Cc);
    fprintf('  Rc=%.3f kΩ\n\n', Rc);
    
    fprintf('【优化成果】\n');
    fprintf('  ✓ 电流镜匹配: id3 ≥ id5\n');
    fprintf('  ✓ 输出摆幅: VDS2 ≈ -0.9V\n');
    fprintf('  ✓ 补偿电阻: Rc动态更新跟随gm2变化\n');
    fprintf('  ✓ 工艺: SMIC 180nm, L=1μm\n');
    fprintf('=====================================\n');
end

%% 解析UGB和相位裕度（带调试输出）
function [ugb, phase_at_ugb] = parse_ugb_pm_debug(filename)
    % 初始化
    ugb = NaN; phase_at_ugb = NaN;
    
    % 读取文件
    if ~exist(filename, 'file')
        error('文件 %s 不存在', filename);
    end
    
    fprintf('读取文件: %s\n', filename);
    fileContent = fileread(filename);
    lines = strsplit(fileContent, '\n');
    fprintf('文件共有 %d 行\n', length(lines));
    
    % 查找UGB和相位裕度数据
    ugb_line_found = false;
    phase_line_found = false;
    
    for i = 1:length(lines)
        line = strtrim(lines{i});  % 移除前后空格
        
        % 查找UGB
        if contains(line, 'ugb=')
            ugb_line_found = true;
            fprintf('第%d行找到UGB: %s\n', i, line);
            
            tokens = regexp(line, 'ugb=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
            if ~isempty(tokens)
                ugb_str = tokens{1}{1};
                fprintf('提取的UGB字符串: "%s"\n', ugb_str);
                
                % 尝试多种转换方式
                ugb_double = str2double(ugb_str);
                fprintf('str2double结果: %e (类型: %s)\n', ugb_double, class(ugb_double));
                
                ugb_sscanf = sscanf(ugb_str, '%f');
                fprintf('sscanf结果: %e (类型: %s)\n', ugb_sscanf, class(ugb_sscanf));
                
                % 使用最可靠的转换
                ugb = ugb_double;
            else
                fprintf('警告: 无法从行中提取UGB值\n');
            end
        end
        
        % 查找相位裕度
        if contains(line, 'phase_at_ugb=')
            phase_line_found = true;
            fprintf('第%d行找到相位: %s\n', i, line);
            
            tokens = regexp(line, 'phase_at_ugb=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
            if ~isempty(tokens)
                phase_str = tokens{1}{1};
                fprintf('提取的相位字符串: "%s"\n', phase_str);
                phase_at_ugb = str2double(phase_str);
                fprintf('转换后相位值: %f\n', phase_at_ugb);
            else
                fprintf('警告: 无法从行中提取相位值\n');
            end
        end
    end
    
    % 报告查找结果
    if ~ugb_line_found
        fprintf('警告: 未找到包含"ugb="的行\n');
    end
    if ~phase_line_found
        fprintf('警告: 未找到包含"phase_at_ugb="的行\n');
    end
    
    % 检查是否找到了两个参数
    if isnan(ugb)
        error('在文件中未找到UGB数据或转换失败');
    end
    if isnan(phase_at_ugb)
        error('在文件中未找到相位裕度数据或转换失败');
    end
    
    fprintf('最终提取值 - UGB: %e Hz, 相位: %f 度\n', ugb, phase_at_ugb);
end

%% 解析UGB和相位裕度
function [ugb, phase_at_ugb] = parse_ugb_pm(filename)
    % 初始化
    ugb = NaN; phase_at_ugb = NaN;
    
    % 读取文件
    if ~exist(filename, 'file')
        error('文件 %s 不存在', filename);
    end
    
    fileContent = fileread(filename);
    lines = strsplit(fileContent, '\n');
    
    % 查找UGB和相位裕度数据
    for i = 1:length(lines)
        line = strtrim(lines{i});  % 移除前后空格
        
        % 查找UGB - 精确匹配"ugb= 数值"格式
        if contains(line, 'ugb=')
            tokens = regexp(line, 'ugb=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
            if ~isempty(tokens)
                ugb_str = tokens{1}{1};
                % 直接使用sscanf进行数值转换
                ugb = sscanf(ugb_str, '%f');
            end
        end
        
        % 查找相位裕度
        if contains(line, 'phase_at_ugb=')
            tokens = regexp(line, 'phase_at_ugb=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
            if ~isempty(tokens)
                phase_str = tokens{1}{1};
                phase_at_ugb = str2double(phase_str);
            end
        end
    end
    
    % 检查是否找到了两个参数
    if isnan(ugb)
        error('在文件中未找到UGB数据');
    end
    if isnan(phase_at_ugb)
        error('在文件中未找到相位裕度数据');
    end
end 