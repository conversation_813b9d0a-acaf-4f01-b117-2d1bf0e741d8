# 低通滤波器自动化设计系统

## 概述
本系统实现了一个完整的低通滤波器自动化设计流程，包括：
1. 滤波器噪声优化
2. 运算放大器自动设计
3. HSPICE仿真分析

## 文件说明

### 核心脚本
- **`lpf_integrated_design.m`** - 🆕 **整合版主脚本（推荐使用）**
  - 包含完整设计流程的单一脚本
  - 整合了噪声优化检查、运放设计、滤波器仿真等所有功能
  - 避免了多个脚本之间的重复调用

### 原始分离脚本（可选）
- `lpf_complete_workflow.m` - 完整工作流程控制脚本
- `lpf_design_with_opamp.m` - 滤波器设计与仿真主脚本
- `ideal3_optimize0.m` - 滤波器噪声优化脚本，要拿来调用。
- `opamp_auto_design.m` - 运放自动化设计函数，设计计算过程。注意实际电路中M7和M2的m设定为10了！
- `opamp_design_with_optimization.m` - 运放自动化设计脚本，拿来调用。
- `run_filter_optimize.m` - 滤波器设计脚本
- `run_opamp_design.m` - 运放自动化设计脚本

### 仿真相关
- `LPF.m` - 生成滤波器SPICE网表的函数
- `GBCA.m` - 生成运放SPICE网表的函数

### 输出文件
- `optimal_parameters.mat` - 噪声优化后的参数
- `lpf_integrated_params.mat` - 整合版设计参数（新）
- `lpf_integrated_report.txt` - 整合版综合报告（新）
- `LPF.sp` - SPICE仿真网表
- `LPF.lis` - SPICE仿真结果
- `lpf_frequency_response.png` - 频率响应图

## 使用方法

### 🌟 推荐方法：使用整合版脚本

#### 1. 配置设计参数（可选）
在运行脚本之前，您可以编辑 `lpf_integrated_design.m` 文件开头的用户配置区域：

```matlab
%% ========== 用户可配置参数区域 ==========
% --- 运放设计参数 ---
GBW = 30e6;        % 增益带宽积 (Hz) - 根据需要修改
CL = 5;            % 负载电容 (pF) - 根据需要修改

% --- 噪声优化参数 ---
Wc = 2*pi*5.2e5;   % 滤波器截止频率 (rad/s) - 默认约520kHz
max_area = 11612;  % 最大允许总面积 (μm²) - 根据工艺调整

% --- 其他设计参数 ---
VDD = 1.8;         % 电源电压 (V)
VSS = 0;           % 接地电压 (V)
L = 1;             % 器件长度 (μm)
fc_default = 1e6;  % 默认目标截止频率 (Hz)
C_base = 10;       % 默认基准电容值 (pF)
```

#### 2. 运行设计脚本
```matlab
% 运行整合版设计脚本
lpf_integrated_design
```

这个整合脚本会按顺序执行：
1. **噪声优化检查**
   - 自动检测是否存在优化结果
   - 询问是否使用/重新运行优化
   
2. **运放自动设计**
   - 根据GBW和CL自动计算运放参数
   
3. **滤波器参数配置**
   - 使用优化参数或默认参数
   - 支持频率调整
   
4. **HSPICE仿真**
   - 生成网表并运行仿真
   
5. **结果分析**
   - 解析仿真结果
   - 绘制频率响应曲线
   
6. **报告生成**
   - 保存所有设计参数
   - 生成综合设计报告

### 备选方法：分步执行

#### 步骤1：噪声优化（可选）
```matlab
% 运行噪声优化
ideal3_optimize
```

#### 步骤2：运行工作流程脚本
```matlab
% 运行完整工作流程
lpf_complete_workflow
```

或

```matlab
% 直接运行设计脚本
lpf_design_with_opamp
```

## 整合版脚本的优势

1. **单文件执行**：所有功能集成在一个脚本中，避免多个文件之间的跳转
2. **统一界面**：一致的用户交互体验
3. **减少重复**：避免了重复的参数传递和文件读取
4. **清晰流程**：六个明确的设计步骤，每步都有状态反馈
5. **完整报告**：生成包含所有信息的综合报告

## 参数说明

### 运放设计参数
- `GBW`: 增益带宽积
- `CL`: 负载电容

### 滤波器参数
- 使用噪声优化结果时：
  - 自动加载 `R1, R3, R5, C1, C2, C3`
  - 可选择调整截止频率
  
- 使用默认设计时：
  - 目标截止频率：1MHz
  - 基准电容：10pF
  - 自动计算电阻值

### 噪声优化参数
- 电阻范围：30kΩ - 200kΩ
- 电容范围：0.5pF - 10pF
- 最大面积：11612 μm²
- 优化目标：最小化输入参考噪声

## 设计流程图

```
┌─────────────────────────┐
│  lpf_integrated_design  │
│    (整合版主脚本)        │
└──────────┬──────────────┘
           │
           ▼
┌─────────────────────────┐
│  1. 噪声优化检查        │
│  - 检测优化结果        │
│  - 选择参数来源        │
└──────────┬──────────────┘
           │
           ▼
┌─────────────────────────┐
│  2. 运放自动设计        │
│  - opamp_auto_design()  │
└──────────┬──────────────┘
           │
           ▼
┌─────────────────────────┐
│  3. 滤波器参数配置      │
│  - 优化参数/默认参数    │
│  - 频率调整选项        │
└──────────┬──────────────┘
           │
           ▼
┌─────────────────────────┐
│  4. HSPICE仿真         │
│  - LPF() 函数          │
└──────────┬──────────────┘
           │
           ▼
┌─────────────────────────┐
│  5. 结果分析与可视化    │
│  - 性能指标           │
│  - 频率响应图         │
└──────────┬──────────────┘
           │
           ▼
┌─────────────────────────┐
│  6. 生成综合报告       │
│  - 参数保存           │
│  - 文本报告           │
└─────────────────────────┘
```

## 输出文件说明

### 整合版脚本输出
- `lpf_integrated_params.mat` - 包含所有设计参数的MAT文件
- `lpf_integrated_report.txt` - 综合设计报告
- `lpf_frequency_response.png` - 频率响应曲线图
- `LPF.sp` - SPICE仿真网表
- `LPF.lis` - SPICE仿真结果

### 参数文件结构
```matlab
design_params = 
    opamp:      % 运放参数
        W0, W1, W2, W3, W5, W7, L
        Ibias, Cc, Rc
    filter:     % 滤波器参数
        R1, R3, R5
        C1, C2, C3
        fc_target
        param_source
    timestamp   % 设计时间戳
```

## 注意事项

1. **HSPICE路径设置**
   - 默认路径：`D:\synopsys\Hspice_P-2019.06-SP1-1\WIN64\hspice.exe`
   - 如需修改，请在脚本中设置 `global pathsp` 变量

2. **工艺库文件**
   - 需要 `ms018_v1p9.lib` 文件
   - 放置在MATLAB工作目录中

3. **噪声优化时间**
   - 噪声优化可能需要几分钟时间
   - 使用多起点和多算法以获得最佳结果

4. **参数一致性**
   - CDL网表中的标识（R1, R3, R5等）与优化脚本保持一致
   - 优化结果可直接应用于SPICE仿真

## 故障排除

1. **找不到优化结果**
   - 确认 `optimal_parameters.mat` 文件存在
   - 运行 `ideal3_optimize` 生成优化结果

2. **HSPICE仿真失败**
   - 检查HSPICE路径设置
   - 确认工艺库文件存在
   - 查看 `LPF.lis` 中的错误信息

3. **参数超出范围**
   - 检查电阻电容值是否在允许范围内
   - 调整优化约束条件

## 版本历史

- **v1.0** - 初始版本，分离的脚本文件
- **v1.1** - 添加噪声优化集成
- **v2.0** - 🆕 整合版脚本 `lpf_integrated_design.m`
  - 单文件包含所有功能
  - 改进的用户交互
  - 统一的报告生成 