****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******
Input File: GBCA.sp
lic:
lic: FLEXlm: SDK_12.3
lic: USER:   Dawn                 HOSTNAME: DESKTOP-GGMVCIS
lic: HOSTID: "d4e98a1a77d6"       PID:      23680
lic: Using FLEXlm license file:
lic: 27000@DESKTOP-GGMVCIS
lic: Checkout 1 hspice
lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12
lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@DESKTOP-GGMVCIS
lic:

 init: begin read circuit files, cpu clock= 3.20E-02
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/behave/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/AD/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/XILINX/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/TLINE/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/TI/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/SIGNET/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/PCI/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/LIN_TEC
                         H/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/PET/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/DI0/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/COMLINE
                         /
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/BURR_BR
                         M/
       option search = D:/synopsys/Hspice_P-2019.06-SP1-1/parts/BJT/
       option post =     2.00
       option measfall =     0.00
       option ingold =     2.00
       option nomod
       option accurate
       option probe
 init: end read circuit files, cpu clock= 3.40E-02 peak memory=      62 mb
 init: begin check errors, cpu clock= 3.40E-02
 init: end check errors, cpu clock= 3.50E-02 peak memory=      62 mb
 init: begin setup matrix, pivot=     0 cpu clock= 3.50E-02
       establish matrix -- done, cpu clock= 3.50E-02 peak memory=      62 mb
       re-order matrix -- done, cpu clock= 3.50E-02 peak memory=      62 mb
 init: end setup matrix, cpu clock= 3.60E-02 peak memory=      62 mb
 dcop: begin dcop, cpu clock= 3.60E-02
 dcop: end dcop, cpu clock= 3.70E-02 peak memory=      62 mb tot_iter=      11
 output: GBCA.ma0
 sweep: ac ac0    begin, #sweeps= 181 cpu clock= 3.70E-02
 ac: frequency=1.00e+00
 ac: frequency=7.94e+00
 ac: frequency=6.31e+01
 ac: frequency=5.01e+02
 ac: frequency=3.98e+03
 ac: frequency=3.16e+04
 ac: frequency=2.51e+05
 ac: frequency=2.00e+06
 ac: frequency=1.58e+07
 ac: frequency=1.26e+08
 ac: frequency=1.00e+09
 sweep: ac ac0    end, cpu clock= 8.20E-02 peak memory=      62 mb
>info:         ***** hspice job concluded
 lic: Release hspice token(s)
