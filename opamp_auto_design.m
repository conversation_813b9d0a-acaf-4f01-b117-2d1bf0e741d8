function [W0, W1, W2, W3, W5, W7, <PERSON><PERSON><PERSON>, Cc, Rc] = opamp_auto_design(GBW, CL)
% 两级运放自动化设计函数
% 输入：
%   GBW - 增益带宽积 (Hz)
%   CL - 负载电容 (pF)
% 输出：
%   W0, W1, W2, W3, W5, W7 - 各器件宽度 (μm)
%   Ibias - 偏置电流 (μA)
%   Cc - 补偿电容 (pF)
%   Rc - 补偿电阻 (kΩ)

    %% 基础参数设定
    L = 1;  % 所有晶体管的长度为1μm
    
    %% 补偿电容计算
    Cc = 0.25 * CL;
    
    %% gm/id 设计参数
    gmoverid1 = 18;  % M1的gm/id比值
    gmoverid3 = 12;  % M3的gm/id比值
    gmoverid0 = 12;  % M0的gm/id比值
    gmoverid2 = 12;  % M2的gm/id比值
    
    %% 工艺参数 - SMIC 180nm
    % NMOS gm/id vs id/w 曲线参数 
    idwn16_1=1.55;
    idwn12_1=3.41;
    idwn16_500 = 2.895;   % gm/id=16时的id/w值
    idwn18_500 = 1.85;   % gm/id=16时的id/w值
    idwn12_180 = 18.5;   % gm/id=12时的id/w值
    idwn12_500 = 6.57;   % gm/id=12时的id/w值
    
    % PMOS gm/id vs id/w 曲线参数
    idwp16_1=0.328;
    idwp12_1=0.772;
    idwp12_500 = 1.532;  % gm/id=12时的id/w值
    idwp12_180 = 6.2;  % gm/id=12时的id/w值
    idwp16_180 = 2.6;  % gm/id=12时的id/w值
    idwp13_180 = 4.65;  % gm/id=12时的id/w值

    
    %% 第一级设计
    % 根据GBW和补偿电容确定第一级跨导
    gm1 = 2 * pi * 1.23 * GBW *  Cc * 1e-12;  %多乘以了1.23是为了增大GBW
    
    % 计算第一级电流
    id1 = gm1 / gmoverid1;
    
    % 计算第三管（电流镜）电流
    id3 = 2 * id1;
    
    % 计算偏置电流 (结果保留小数点后一位)
    Ibias = round(id3 * 1e6, 1);
    
    %% 第二级设计
    % 第二极点频率
    P2 = 2.5 * GBW;
    
    % 第二级跨导 (修正：使用CL而不是Cc)
    gm2 = 2 * pi * 1.3 * P2 * CL * 1e-12;
    
    % 第二级电流
    id2 = gm2 / gmoverid2;
    
    % 补偿电阻计算 (单位为kΩ，结果保留三位小数)
    % 注意: 这是初始Rc值，在优化过程中当W2变化时，Rc会根据新的gm2动态更新
    Rc = round((1/gm2 + 1)*1e-3, 3);
    
    %% 第零管设计
    gm0 = gmoverid0 * id1;
    gm3 = gmoverid3 * id3;
    
    %% 器件尺寸计算 (所有W结果保留小数点后一位)
    % M1宽度 (NMOS, gm/id=16)
    W1 = round(gm1 * 1e6 / (gmoverid1 * idwn18_500), 1);
    
    % M3宽度 (NMOS, gm/id=12)
    W3 = round(gm3 * 1e6 / (gmoverid3 * idwn12_500), 1);

    % M5宽度 (NMOS, MNM5电流镜，与W3相等)
    W5 = W3;
    
    % M7宽度 (NMOS, MNM4第二级电流源)
    W7 = round(id2 * 1e6 / idwn12_180, 1);
    
    % M2宽度 (PMOS, gm/id=12)
    W2 = round(gm2 * 1e6 / (gmoverid2 * idwp12_180), 1);
    
    % M0宽度 (PMOS, gm/id=12)
    W0 = round(gm0 * 1e6 / (gmoverid0 * idwp12_500), 1);
    
    %% 显示设计结果
    fprintf('=== 两级运放自动化设计结果 ===\n');
    fprintf('输入规格:\n');
    fprintf('  GBW = %.0f MHz\n', GBW/1e6);
    fprintf('  CL = %.1f pF\n', CL);
    fprintf('\n设计参数:\n');
    fprintf('  Cc = %.2f pF\n', Cc);
    fprintf('  Ibias = %.1f μA\n', Ibias);
    fprintf('  Rc = %.3f kΩ\n', Rc);
    fprintf('\n器件尺寸:\n');
    fprintf('  W0 = %.1f μm (PMOS 电流镜)\n', W0);
    fprintf('  W1 = %.1f μm (NMOS 输入管)\n', W1);
    fprintf('  W2 = %.1f μm (PMOS 第二级)\n', W2);
    fprintf('  W3 = %.1f μm (NMOS 电流镜)\n', W3);
    fprintf('  W5 = %.1f μm (NMOS 电流镜，与W3匹配)\n', W5);
    fprintf('  W7 = %.1f μm (NMOS 第二级电流源)\n', W7);
    fprintf('  L = %.0f μm (所有器件)\n', L);

end 