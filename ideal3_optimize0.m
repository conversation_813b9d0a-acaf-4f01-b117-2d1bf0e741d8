clc;
clear;
close all;

% 声明全局变量
global WC_GLOBAL MAX_AREA_GLOBAL;

%% 定义常数和参数
k = 1.38e-23;   % 玻尔兹曼常数 (J/K)
T = 300;        % 温度 (K)

% 参数初始化 - 尝试从多种来源获取参数
fprintf('正在尝试获取参数...\n');

% 方法1：全局变量
if exist('WC_GLOBAL', 'var') && ~isempty(WC_GLOBAL)
    Wc = WC_GLOBAL;
    fprintf('✓ 从全局变量获取Wc = %.2f rad/s (%.2f kHz)\n', Wc, Wc/(2*pi*1000));
    wc_source = '全局变量';
else
    wc_source = '';
end

if exist('MAX_AREA_GLOBAL', 'var') && ~isempty(MAX_AREA_GLOBAL)
    max_area = MAX_AREA_GLOBAL;
    fprintf('✓ 从全局变量获取max_area = %.2f μm²\n', max_area);
    area_source = '全局变量';
else
    area_source = '';
end

% 方法2：工作空间变量
if ~exist('Wc', 'var') || isempty(Wc)
    if exist('Wc_user', 'var')
        Wc = Wc_user;
        fprintf('✓ 从工作空间变量获取Wc = %.2f rad/s (%.2f kHz)\n', Wc, Wc/(2*pi*1000));
        wc_source = '工作空间变量';
    end
end

if ~exist('max_area', 'var') || isempty(max_area)
    if exist('max_area_user', 'var')
        max_area = max_area_user;
        fprintf('✓ 从工作空间变量获取max_area = %.2f μm²\n', max_area);
        area_source = '工作空间变量';
    end
end

% 方法3：Base工作空间变量
if ~exist('Wc', 'var') || isempty(Wc)
    if evalin('base', 'exist(''Wc_user'', ''var'')')
        Wc = evalin('base', 'Wc_user');
        fprintf('✓ 从Base工作空间获取Wc = %.2f rad/s (%.2f kHz)\n', Wc, Wc/(2*pi*1000));
        wc_source = 'Base工作空间';
    end
end

if ~exist('max_area', 'var') || isempty(max_area)
    if evalin('base', 'exist(''max_area_user'', ''var'')')
        max_area = evalin('base', 'max_area_user');
        fprintf('✓ 从Base工作空间获取max_area = %.2f μm²\n', max_area);
        area_source = 'Base工作空间';
    end
end

% 方法4：临时参数文件
if (~exist('Wc', 'var') || isempty(Wc) || ~exist('max_area', 'var') || isempty(max_area)) && exist('temp_params.mat', 'file')
    temp_data = load('temp_params.mat');
    if ~exist('Wc', 'var') || isempty(Wc)
        if isfield(temp_data, 'Wc')
            Wc = temp_data.Wc;
            fprintf('✓ 从临时文件获取Wc = %.2f rad/s (%.2f kHz)\n', Wc, Wc/(2*pi*1000));
            wc_source = '临时文件';
        end
    end
    
    if ~exist('max_area', 'var') || isempty(max_area)
        if isfield(temp_data, 'max_area')
            max_area = temp_data.max_area;
            fprintf('✓ 从临时文件获取max_area = %.2f μm²\n', max_area);
            area_source = '临时文件';
        end
    end
end

% 如果上述方法都失败，使用默认值
if ~exist('Wc', 'var') || isempty(Wc)
    Wc = 2*pi*5.2e5; % 滤波器角频率，约520kHz
    fprintf('! 使用默认Wc = %.2f rad/s (%.2f kHz)\n', Wc, Wc/(2*pi*1000));
    wc_source = '默认值';
end

if ~exist('max_area', 'var') || isempty(max_area)
    max_area = 11612; % 最大允许总面积 μm²
    fprintf('! 使用默认max_area = %.2f μm²\n', max_area);
    area_source = '默认值';
end

% 显示最终使用的参数和来源
fprintf('\n最终使用的参数:\n');
fprintf('  Wc = %.2f rad/s (%.2f kHz) [来源: %s]\n', Wc, Wc/(2*pi*1000), wc_source);
fprintf('  max_area = %.2f μm² [来源: %s]\n\n', max_area, area_source);

%% 设置电阻和电容的合理范围
R_min = 30e3;    % 最小电阻值 (Ω)
R_max = 200e3;   % 最大电阻值 (Ω) 
C_min = 0.5e-12; % 最小电容值 (F)
C_max = 10e-12;  % 最大电容值 (F)

%% 定义面积约束
R_den = 1e3;     % 电阻面积密度 (Ω/μm²)
C_den = 1e-15;   % 电容面积密度 (F/μm²)

%% 探索噪声函数与电阻值的关系
% 创建一个更精细的网格来测试噪声与电阻值的关系
fprintf('分析噪声与电阻值的关系...\n');
test_points = 10; % 增加测试点数量以获得更精确的初始估计
R_test = logspace(log10(R_min), log10(R_max), test_points); % 使用对数间隔以更好地覆盖范围

% 记录所有满足约束的点及其噪声值
valid_points = [];
valid_noise = [];

% 计算不同电阻值组合下的噪声
fprintf('执行粗网格搜索...\n');
for i = 1:test_points
    for j = 1:test_points
        for l = 1:test_points
            R1_test = R_test(i);
            R3_test = R_test(j);
            R5_test = R_test(l);
            
            % 检查约束
            R0_test = R1_test/2;
            
            % 如果R0太小，跳过此组合
            if R0_test < R_min
                continue;
            end
            
            % 计算电容
            [C1_test, C2_test, C3_test] = calculateExactCapacitors(R1_test, R3_test, R5_test, Wc);
            
            % 检查电容是否在允许范围内
            if C1_test < C_min || C1_test > C_max || ...
               C2_test < C_min || C2_test > C_max || ...
               C3_test < C_min || C3_test > C_max
                continue;
            end
            
            % 检查面积约束
            x_test = [R1_test, R3_test, R5_test, C1_test, C2_test, C3_test];
            [area_test, ~] = calculateTotalAreaNew(x_test, R_den, C_den);
            
            if area_test > max_area
                continue;
            end
            
            % 计算噪声
            noise_test = calculateInputNoiseNew(R1_test, R3_test, R5_test, k, T);
            
            % 保存有效点
            valid_points = [valid_points; R1_test, R3_test, R5_test, C1_test, C2_test, C3_test, area_test];
            valid_noise = [valid_noise; noise_test];
        end
    end
end

fprintf('找到 %d 个满足所有约束的点\n', length(valid_noise));

if ~isempty(valid_noise)
    % 找到最小噪声点
    [min_noise_val, min_idx] = min(valid_noise);
    best_point = valid_points(min_idx, :);
    
    R1_min_noise = best_point(1);
    R3_min_noise = best_point(2);
    R5_min_noise = best_point(3);
    area_min_noise = best_point(7);
    
    fprintf('网格搜索找到的最小噪声点:\n');
    fprintf('R1 = %.2f kΩ, R3 = %.2f kΩ, R5 = %.2f kΩ\n', ...
        R1_min_noise/1e3, R3_min_noise/1e3, R5_min_noise/1e3);
    fprintf('噪声值: %.4e, 面积: %.2f μm²\n\n', min_noise_val, area_min_noise);
    
    % 使用这个点作为初始值
    R1_init = R1_min_noise;
    R3_init = R3_min_noise;
    R5_init = R5_min_noise;
    
    % 进行细网格搜索 - 在最佳点附近搜索
    fprintf('执行细网格搜索...\n');
    range_factor = 0.2; % 搜索范围为最佳值的±20%
    
    % 生成细网格
    fine_points = 5;
    R1_fine = linspace(max(R_min, R1_init*(1-range_factor)), min(R_max, R1_init*(1+range_factor)), fine_points);
    R3_fine = linspace(max(R_min, R3_init*(1-range_factor)), min(R_max, R3_init*(1+range_factor)), fine_points);
    R5_fine = linspace(max(R_min, R5_init*(1-range_factor)), min(R_max, R5_init*(1+range_factor)), fine_points);
    
    % 清空之前的结果
    valid_points = [];
    valid_noise = [];
    
    % 在细网格上搜索
    for i = 1:fine_points
        for j = 1:fine_points
            for l = 1:fine_points
                R1_test = R1_fine(i);
                R3_test = R3_fine(j);
                R5_test = R5_fine(l);
                
                % 检查约束
                R0_test = R1_test/2;
                if R0_test < R_min
                    continue;
                end
                
                % 计算电容
                [C1_test, C2_test, C3_test] = calculateExactCapacitors(R1_test, R3_test, R5_test, Wc);
                
                % 检查电容约束
                if C1_test < C_min || C1_test > C_max || ...
                   C2_test < C_min || C2_test > C_max || ...
                   C3_test < C_min || C3_test > C_max
                    continue;
                end
                
                % 检查面积约束
                x_test = [R1_test, R3_test, R5_test, C1_test, C2_test, C3_test];
                [area_test, ~] = calculateTotalAreaNew(x_test, R_den, C_den);
                
                if area_test > max_area
                    continue;
                end
                
                % 计算噪声
                noise_test = calculateInputNoiseNew(R1_test, R3_test, R5_test, k, T);
                
                % 保存有效点
                valid_points = [valid_points; R1_test, R3_test, R5_test, C1_test, C2_test, C3_test, area_test];
                valid_noise = [valid_noise; noise_test];
            end
        end
    end
    
    if ~isempty(valid_noise)
        % 找到最小噪声点
        [min_noise_val, min_idx] = min(valid_noise);
        best_point = valid_points(min_idx, :);
        
        R1_min_noise = best_point(1);
        R3_min_noise = best_point(2);
        R5_min_noise = best_point(3);
        area_min_noise = best_point(7);
        
        fprintf('细网格搜索找到的最小噪声点:\n');
        fprintf('R1 = %.2f kΩ, R3 = %.2f kΩ, R5 = %.2f kΩ\n', ...
            R1_min_noise/1e3, R3_min_noise/1e3, R5_min_noise/1e3);
        fprintf('噪声值: %.4e, 面积: %.2f μm²\n\n', min_noise_val, area_min_noise);
        
        % 更新初始值
        R1_init = R1_min_noise;
        R3_init = R3_min_noise;
        R5_init = R5_min_noise;
    else
        fprintf('细网格搜索未找到满足约束的点，使用粗网格的最佳点\n');
    end
else
    % 如果网格搜索未找到有效点，使用默认值
    fprintf('网格搜索未找到满足所有约束的点，使用默认初始值\n\n');
    R1_init = 200e3;
    R3_init = 100e3;
    R5_init = 100e3;
end

%% 计算关联参数的初始值
% 计算其他电阻的初始值
R0_init = R1_init/2;  % R0 = R1/2
R2_init = R1_init;    % R2 = R1
R4_init = R3_init;    % R4 = R3
R6_init = R5_init;    % R6 = R5

% 根据初始电阻值计算电容值
[C1_init, C2_init, C3_init] = calculateExactCapacitors(R1_init, R3_init, R5_init, Wc);

% 计算初始噪声
x_init = [R1_init, R3_init, R5_init, C1_init, C2_init, C3_init];
initial_noise = calculateInputNoiseNew(R1_init, R3_init, R5_init, k, T);

% 验证约束
disp('初始参数约束检查:');
[c, ceq] = constraints(x_init, R_min, R_max, C_min, C_max, R_den, C_den, max_area);
disp(['等式约束违反度(理想值为0): ', num2str(norm(ceq))]);
if ~isempty(c)
    disp(['不等式约束违反度(应小于0): ', num2str(max(c))]);
    
    % 显示哪些约束被违反
    violated = find(c > 0);
    if ~isempty(violated)
        for i = 1:length(violated)
            fprintf('约束 %d 被违反: %f\n', violated(i), c(violated(i)));
        end
        
        % 自动调整初始点以满足约束
        disp('尝试调整初始值以满足约束...');
        
        % 缩小电阻值以满足约束
        scale_factor = 0.9;
        max_attempts = 20;
        
        for attempt = 1:max_attempts
            % 缩小电阻值
            R1_init = R1_init * scale_factor;
            R3_init = R3_init * scale_factor;
            R5_init = R5_init * scale_factor;
            
            % 计算新的电容值
            [C1_init, C2_init, C3_init] = calculateExactCapacitors(R1_init, R3_init, R5_init, Wc);
            
            % 重新计算R0
            R0_init = R1_init/2;
            
            % 检查R0是否满足最小值约束
            if R0_init < R_min
                fprintf('警告: R0 = %.2f kΩ 低于最小值 %.2f kΩ\n', R0_init/1e3, R_min/1e3);
                % 增加R1使R0满足约束
                R1_init = 2 * R_min;
                R0_init = R1_init/2;
                fprintf('调整R1 = %.2f kΩ 以使R0 = %.2f kΩ\n', R1_init/1e3, R0_init/1e3);
                
                % 重新计算电容
                [C1_init, C2_init, C3_init] = calculateExactCapacitors(R1_init, R3_init, R5_init, Wc);
            end
            
            % 更新参数向量
            x_init = [R1_init, R3_init, R5_init, C1_init, C2_init, C3_init];
            
            % 重新检查约束
            [c, ~] = constraints(x_init, R_min, R_max, C_min, C_max, R_den, C_den, max_area);
            
            if all(c <= 0)
                fprintf('已找到满足约束的初始点 (尝试 %d/%d):\n', attempt, max_attempts);
                fprintf('R1 = %.2f kΩ, R3 = %.2f kΩ, R5 = %.2f kΩ\n', R1_init/1e3, R3_init/1e3, R5_init/1e3);
                fprintf('C1 = %.2f pF, C2 = %.2f pF, C3 = %.2f pF\n', C1_init/1e-12, C2_init/1e-12, C3_init/1e-12);
                break;
            end
            
            if attempt == max_attempts
                warning('无法找到满足约束的初始点。将放宽约束条件。');
            end
        end
    end
end

% 面积计算
[area, area_components] = calculateTotalAreaNew(x_init, R_den, C_den);
disp(['初始总面积: ', num2str(area), ' μm² (最大允许: ', num2str(max_area), ' μm²)']);
disp(['初始噪声: ', num2str(initial_noise)]);

% 验证电容是否在合理范围内
disp('初始电容值:');
fprintf('C1 = %.2f pF (范围: %.2f-%.2f pF)\n', C1_init/1e-12, C_min/1e-12, C_max/1e-12);
fprintf('C2 = %.2f pF (范围: %.2f-%.2f pF)\n', C2_init/1e-12, C_min/1e-12, C_max/1e-12);
fprintf('C3 = %.2f pF (范围: %.2f-%.2f pF)\n', C3_init/1e-12, C_min/1e-12, C_max/1e-12);

% 验证电阻是否在合理范围内
disp('初始电阻值:');
fprintf('R0 = %.2f kΩ (范围: %.2f-%.2f kΩ)\n', R0_init/1e3, R_min/1e3, R_max/1e3);
fprintf('R1 = %.2f kΩ (范围: %.2f-%.2f kΩ)\n', R1_init/1e3, R_min/1e3, R_max/1e3);
fprintf('R3 = %.2f kΩ (范围: %.2f-%.2f kΩ)\n', R3_init/1e3, R_min/1e3, R_max/1e3);
fprintf('R5 = %.2f kΩ (范围: %.2f-%.2f kΩ)\n', R5_init/1e3, R_min/1e3, R_max/1e3);

%% 多阶段、多算法优化
% 定义多种优化算法
fprintf('\n开始优化过程...\n');

% 设置优化变量的边界
lb = [R_min, R_min, R_min];
ub = [R_max, R_max, R_max];

% 检查R0约束
if R_min/2 < R_min
    lb(1) = 2*R_min; % 确保R0 = R1/2 >= R_min
    fprintf('调整R1的最小值为 %.2f kΩ，以确保R0 >= %.2f kΩ\n', lb(1)/1e3, R_min/1e3);
end

% 设置所有算法的共同优化选项
options_common = optimoptions('fmincon', ...
    'Display', 'iter', ...
    'MaxIterations', 3000, ...
    'MaxFunctionEvaluations', 10000, ...
    'OptimalityTolerance', 1e-10, ...
    'ConstraintTolerance', 1e-8, ...
    'StepTolerance', 1e-12, ...
    'UseParallel', false, ...
    'FiniteDifferenceType', 'central');

% 创建不同的算法配置
algorithms = {
    'sqp', 
    'interior-point', 
    'active-set'
};

% 使用不同起点进行多次优化尝试
n_start_points = 10; % 减少起点数但提高每个起点的优化质量

% 为更好的探索，创建一组多样化的起点
start_points = zeros(n_start_points, 3);

% 第一个起点使用网格搜索找到的最佳点或默认初始值
start_points(1, :) = [R1_init, R3_init, R5_init];

% 生成其他起点 - 使用拉丁超立方采样以获得更好的覆盖
rng(42); % 设置随机种子以确保可重复性
lhs_samples = lhsdesign(n_start_points-1, 3);

for i = 2:n_start_points
    % 将[0,1]的LHS样本映射到电阻范围
    r = lhs_samples(i-1, :);
    start_points(i, :) = [R_min, R_min, R_min] + r .* (R_max - R_min);
    
    % 确保R1满足R0约束
    if start_points(i, 1)/2 < R_min
        start_points(i, 1) = 2*R_min;
    end
end

% 存储结果
all_results = cell(n_start_points * length(algorithms), 7); % x, fval, exitflag, output, constraint_violation, algorithm, start_point
result_idx = 0;
valid_count = 0;

% 使用不同起点和算法进行多阶段优化
for i = 1:n_start_points
    x0 = start_points(i, :);
    fprintf('\n==== 优化起点 %d/%d: R1=%.2f kΩ, R3=%.2f kΩ, R5=%.2f kΩ ====\n', ...
        i, n_start_points, x0(1)/1e3, x0(2)/1e3, x0(3)/1e3);
    
    % 第1阶段：使用当前起点进行初步优化
    best_x_stage1 = [];
    best_fval_stage1 = Inf;
    best_algo_stage1 = '';
    
    for a = 1:length(algorithms)
        algo = algorithms{a};
        fprintf('算法: %s\n', algo);
        
        % 设置算法特定选项
        options = optimoptions(options_common, 'Algorithm', algo);
        
        % 定义目标函数
        objective = @(x) calculateInputNoiseNew(x(1), x(2), x(3), k, T);
        
        % 定义非线性约束函数
        nonlcon = @(x) optimize_constraints(x, R_min, R_max, C_min, C_max, R_den, C_den, max_area, Wc);
        
        try
            % 第一阶段优化
            [x, fval, exitflag, output] = fmincon(objective, x0, [], [], [], [], lb, ub, nonlcon, options);
            
            % 计算完整的参数向量
            R1 = x(1);
            R3 = x(2);
            R5 = x(3);
            [C1, C2, C3] = calculateExactCapacitors(R1, R3, R5, Wc);
            x_full = [R1, R3, R5, C1, C2, C3];
            
            % 验证约束
            [c, ceq] = constraints(x_full, R_min, R_max, C_min, C_max, R_den, C_den, max_area);
            constraint_violation = max(max(c), 0) + norm(ceq);
            
            % 增加结果索引
            result_idx = result_idx + 1;
            
            % 存储结果
            all_results{result_idx, 1} = x_full;
            all_results{result_idx, 2} = fval;
            all_results{result_idx, 3} = exitflag;
            all_results{result_idx, 4} = output;
            all_results{result_idx, 5} = constraint_violation;
            all_results{result_idx, 6} = algo;
            all_results{result_idx, 7} = i;
            
            if constraint_violation <= 1e-6
                valid_count = valid_count + 1;
                fprintf('✓ 第1阶段 - 找到有效解: 噪声 = %.4e, 约束满足度 = %.2e\n', fval, constraint_violation);
                
                % 如果这是当前起点最好的解，则保存
                if fval < best_fval_stage1
                    best_fval_stage1 = fval;
                    best_x_stage1 = x;
                    best_algo_stage1 = algo;
                end
            else
                fprintf('✗ 第1阶段 - 找到无效解: 噪声 = %.4e, 约束违反度 = %.2e\n', fval, constraint_violation);
            end
        catch ME
            fprintf('✗ 算法 %s 优化失败: %s\n', algo, ME.message);
        end
    end
    
    % 第2阶段：使用第1阶段的最佳结果作为起点，尝试更细致的优化
    if ~isempty(best_x_stage1)
        fprintf('\n第2阶段优化 - 使用第1阶段最佳结果 (算法: %s, 噪声: %.4e)\n', best_algo_stage1, best_fval_stage1);
        
        % 使用更严格的优化选项
        options_stage2 = optimoptions(options_common, ...
            'Algorithm', 'sqp', ...
            'OptimalityTolerance', 1e-12, ...
            'ConstraintTolerance', 1e-10, ...
            'StepTolerance', 1e-14, ...
            'MaxIterations', 5000);
        
        try
            % 使用第1阶段的最佳结果作为起点
            [x, fval, exitflag, output] = fmincon(objective, best_x_stage1, [], [], [], [], lb, ub, nonlcon, options_stage2);
            
            % 计算完整的参数向量
            R1 = x(1);
            R3 = x(2);
            R5 = x(3);
            [C1, C2, C3] = calculateExactCapacitors(R1, R3, R5, Wc);
            x_full = [R1, R3, R5, C1, C2, C3];
            
            % 验证约束
            [c, ceq] = constraints(x_full, R_min, R_max, C_min, C_max, R_den, C_den, max_area);
            constraint_violation = max(max(c), 0) + norm(ceq);
            
            % 增加结果索引
            result_idx = result_idx + 1;
            
            % 存储结果
            all_results{result_idx, 1} = x_full;
            all_results{result_idx, 2} = fval;
            all_results{result_idx, 3} = exitflag;
            all_results{result_idx, 4} = output;
            all_results{result_idx, 5} = constraint_violation;
            all_results{result_idx, 6} = 'sqp-refined';
            all_results{result_idx, 7} = i;
            
            if constraint_violation <= 1e-6
                valid_count = valid_count + 1;
                fprintf('✓ 第2阶段 - 找到有效解: 噪声 = %.4e, 约束满足度 = %.2e\n', fval, constraint_violation);
                fprintf('  噪声改善: %.2f%%\n', 100*(1 - fval/best_fval_stage1));
            else
                fprintf('✗ 第2阶段 - 找到无效解: 噪声 = %.4e, 约束违反度 = %.2e\n', fval, constraint_violation);
            end
        catch ME
            fprintf('✗ 第2阶段优化失败: %s\n', ME.message);
        end
    end
end

%% 处理优化结果
fprintf('\n优化完成，处理结果...\n');

% 删除空结果
all_results = all_results(1:result_idx, :);
fprintf('共执行 %d 次优化\n', result_idx);
fprintf('找到 %d 个有效解（满足所有约束）\n', valid_count);

% 提取所有有效解
valid_solutions = cell(valid_count, 7);
valid_idx = 0;

for i = 1:result_idx
    x_full = all_results{i, 1};
    fval = all_results{i, 2};
    constraint_violation = all_results{i, 5};
    
    if ~isempty(x_full) && constraint_violation <= 1e-6
        valid_idx = valid_idx + 1;
        valid_solutions(valid_idx, :) = all_results(i, :);
    end
end

% 找出最佳解
if valid_idx > 0
    % 提取噪声值
    noise_values = cellfun(@(x) x, valid_solutions(:, 2));
    
    % 找出最小噪声的解
    [min_noise, best_idx] = min(noise_values);
    
    % 获取最佳解的详细信息
    best_x = valid_solutions{best_idx, 1};
    best_exitflag = valid_solutions{best_idx, 3};
    best_output = valid_solutions{best_idx, 4};
    best_violation = valid_solutions{best_idx, 5};
    best_algorithm = valid_solutions{best_idx, 6};
    best_start_point = valid_solutions{best_idx, 7};
    
    % 提取参数
    R1 = best_x(1);
    R3 = best_x(2);
    R5 = best_x(3);
    C1 = best_x(4);
    C2 = best_x(5);
    C3 = best_x(6);
    
    % 计算派生参数
    R0 = R1/2;
    R2 = R1;
    R4 = R3;
    R6 = R5;
    
    % 计算面积
    [area, area_components] = calculateTotalAreaNew(best_x, R_den, C_den);
    
    % 计算初始噪声进行比较
    initial_R1 = 200e3;
    initial_R3 = 100e3;
    initial_R5 = 100e3;
    initial_noise = calculateInputNoiseNew(initial_R1, initial_R3, initial_R5, k, T);
    
    % 显示结果
    fprintf('\n======== 最佳解决方案 ========\n');
    fprintf('算法: %s, 起点索引: %d, 退出标志: %d\n', best_algorithm, best_start_point, best_exitflag);
    fprintf('噪声: %.4e (相对于标准初始值 %.4e 改善: %.2f%%)\n', ...
        min_noise, initial_noise, 100*(1 - min_noise/initial_noise));
    fprintf('约束满足度: %.2e\n', best_violation);
    fprintf('面积: %.2f μm² (最大允许: %.2f μm²)\n', area, max_area);
    fprintf('面积利用率: %.2f%%\n', 100*area/max_area);
    fprintf('迭代次数: %d, 函数评估次数: %d\n', ...
        best_output.iterations, best_output.funcCount);
    
    % 打印详细参数
    fprintf('\n最佳参数详情:\n');
    fprintf('R0: %.3f kΩ - %s\n', R0/1e3, checkRange(R0, R_min, R_max));
    fprintf('R1: %.3f kΩ - %s\n', R1/1e3, checkRange(R1, R_min, R_max));
    fprintf('R2: %.3f kΩ - %s\n', R2/1e3, checkRange(R2, R_min, R_max));
    fprintf('R3: %.3f kΩ - %s\n', R3/1e3, checkRange(R3, R_min, R_max));
    fprintf('R4: %.3f kΩ - %s\n', R4/1e3, checkRange(R4, R_min, R_max));
    fprintf('R5: %.3f kΩ - %s\n', R5/1e3, checkRange(R5, R_min, R_max));
    fprintf('R6: %.3f kΩ - %s\n', R6/1e3, checkRange(R6, R_min, R_max));
    
    fprintf('C1: %.3f pF - %s\n', C1/1e-12, checkRange(C1, C_min, C_max));
    fprintf('C2: %.3f pF - %s\n', C2/1e-12, checkRange(C2, C_min, C_max));
    fprintf('C3: %.3f pF - %s\n', C3/1e-12, checkRange(C3, C_min, C_max));
    
    fprintf('电阻总面积: %.2f μm² (%.2f%%)\n', area_components(1), 100*area_components(1)/area);
    fprintf('电容总面积: %.2f μm² (%.2f%%)\n', area_components(2), 100*area_components(2)/area);
    fprintf('总面积: %.2f μm²\n', area);
    
    % 将最佳解的噪声公式详细计算展示
    term1 = 4 * k * T * R1;                  % R1热噪声
    term2 = 4 * k * T * R3 * 0.5;            % R3热噪声
    term3 = 4 * k * T * R5 * 0.5;            % R5热噪声
    fprintf('\n噪声计算详情:\n');
    fprintf('R1的热噪声贡献: %.4e (%.2f%%)\n', term1, 100*term1/min_noise);
    fprintf('R3的热噪声贡献: %.4e (%.2f%%)\n', term2, 100*term2/min_noise);
    fprintf('R5的热噪声贡献: %.4e (%.2f%%)\n', term3, 100*term3/min_noise);
    fprintf('总噪声: %.4e\n', min_noise);
    
    % 最后将优化结果保存起来，以便其他脚本使用
    fprintf('\n将优化结果保存为optimal_parameters.mat...\n');
    save('optimal_parameters.mat', 'R1', 'R3', 'R5', 'C1', 'C2', 'C3', 'min_noise', 'area', 'initial_noise');
    fprintf('✓ 优化结果已保存\n');

    % 使用global变量传递优化结果
    global OPT_R1 OPT_R3 OPT_R5 OPT_C1 OPT_C2 OPT_C3;
    global OPT_MIN_NOISE OPT_AREA OPT_INITIAL_NOISE;

    OPT_R1 = R1;
    OPT_R3 = R3;
    OPT_R5 = R5;
    OPT_C1 = C1;
    OPT_C2 = C2;
    OPT_C3 = C3;
    OPT_MIN_NOISE = min_noise;
    OPT_AREA = area;
    OPT_INITIAL_NOISE = initial_noise;
    fprintf('✓ 优化结果已保存到全局变量\n');

    % 保存到base工作空间
    assignin('base', 'opt_R1', R1);
    assignin('base', 'opt_R3', R3);
    assignin('base', 'opt_R5', R5);
    assignin('base', 'opt_C1', C1);
    assignin('base', 'opt_C2', C2);
    assignin('base', 'opt_C3', C3);
    assignin('base', 'opt_min_noise', min_noise);
    assignin('base', 'opt_area', area);
    assignin('base', 'opt_initial_noise', initial_noise);
    fprintf('✓ 优化结果已保存到base工作空间\n');
    
    % 从704行左右开始删除绘图代码
    fprintf('\n参数对比表格:\n');
    fprintf('%-8s | %-12s | %-12s | %-8s\n', '参数', '初始值', '优化值', '变化率');
    fprintf('%-8s | %-12s | %-12s | %-8s\n', '--------', '------------', '------------', '--------');
    fprintf('%-8s | %-12.3f | %-12.3f | %-8.2f%%\n', 'R0 (kΩ)', initial_R1/2/1e3, R0/1e3, 100*(R0-initial_R1/2)/(initial_R1/2));
    fprintf('%-8s | %-12.3f | %-12.3f | %-8.2f%%\n', 'R1 (kΩ)', initial_R1/1e3, R1/1e3, 100*(R1-initial_R1)/initial_R1);
    fprintf('%-8s | %-12.3f | %-12.3f | %-8.2f%%\n', 'R3 (kΩ)', initial_R3/1e3, R3/1e3, 100*(R3-initial_R3)/initial_R3);
    fprintf('%-8s | %-12.3f | %-12.3f | %-8.2f%%\n', 'R5 (kΩ)', initial_R5/1e3, R5/1e3, 100*(R5-initial_R5)/initial_R5);
    
    % 计算初始值的电容和面积
    [C1_init, C2_init, C3_init] = calculateExactCapacitors(initial_R1, initial_R3, initial_R5, Wc);
    x_init = [initial_R1, initial_R3, initial_R5, C1_init, C2_init, C3_init];
    [area_init, components_init] = calculateTotalAreaNew(x_init, R_den, C_den);
    
    fprintf('%-8s | %-12.3f | %-12.3f | %-8.2f%%\n', 'C1 (pF)', C1_init/1e-12, C1/1e-12, 100*(C1-C1_init)/C1_init);
    fprintf('%-8s | %-12.3f | %-12.3f | %-8.2f%%\n', 'C2 (pF)', C2_init/1e-12, C2/1e-12, 100*(C2-C2_init)/C2_init);
    fprintf('%-8s | %-12.3f | %-12.3f | %-8.2f%%\n', 'C3 (pF)', C3_init/1e-12, C3/1e-12, 100*(C3-C3_init)/C3_init);
    fprintf('%-8s | %-12.3f | %-12.3f | %-8.2f%%\n', '面积 (μm²)', area_init, area, 100*(area-area_init)/area_init);
    fprintf('%-8s | %-12.4e | %-12.4e | %-8.2f%%\n', '噪声', initial_noise, min_noise, 100*(min_noise-initial_noise)/initial_noise);
    
    fprintf('优化完成\n');
else
    fprintf('\n未找到满足约束的有效解决方案。\n');
    fprintf('请尝试放宽约束条件或调整参数范围。\n');
end

%% 辅助函数：检查参数是否在范围内
function result = checkRange(value, min_val, max_val)
    if value < min_val
        result = sprintf('低于最小值 (%.3f)', min_val);
    elseif value > max_val
        result = sprintf('高于最大值 (%.3f)', max_val);
    else
        result = '在允许范围内';
    end
end

%% 约束函数 - 用于优化器
function [c, ceq] = optimize_constraints(x, R_min, R_max, C_min, C_max, R_den, C_den, max_area, Wc)
    % 提取电阻值
    R1 = x(1);
    R3 = x(2);
    R5 = x(3);
    
    % 计算对应的电容值
    [C1, C2, C3] = calculateExactCapacitors(R1, R3, R5, Wc);
    
    % 创建完整的参数向量
    x_full = [R1, R3, R5, C1, C2, C3];
    
    % 调用通用约束函数
    [c, ceq] = constraints(x_full, R_min, R_max, C_min, C_max, R_den, C_den, max_area);
end

%% 约束函数 - 通用版本
function [c, ceq] = constraints(x, R_min, R_max, C_min, C_max, R_den, C_den, max_area)
    % 提取参数
    R1 = x(1);
    R3 = x(2);
    R5 = x(3);
    C1 = x(4);
    C2 = x(5);
    C3 = x(6);
    
    % 没有等式约束
    ceq = []; 
    
    % 计算其他电阻
    R0 = R1/2;  % R0 = R1/2
    R2 = R1;    % R2 = R1
    R4 = R3;    % R4 = R3
    R6 = R5;    % R6 = R5
    
    % 计算总面积
    [area, ~] = calculateTotalAreaNew(x, R_den, C_den);
    
    % 不等式约束 c <= 0
    c = [
        % 面积约束
        area - max_area;
        
        % R0的范围约束
        R_min - R0;  % R0 >= R_min
        R0 - R_max;  % R0 <= R_max
        
        % 优化变量R1, R3, R5的约束 - 这些可能是多余的，因为已经有边界约束
        % 但保留以确保完整的约束检查
        R_min - R1;  % R1 >= R_min
        R1 - R_max;  % R1 <= R_max
        R_min - R3;  % R3 >= R_min
        R3 - R_max;  % R3 <= R_max
        R_min - R5;  % R5 >= R_min
        R5 - R_max;  % R5 <= R_max
        
        % 额外电阻的范围约束
        R_min - R2;  % R2 >= R_min
        R2 - R_max;  % R2 <= R_max
        R_min - R4;  % R4 >= R_min
        R4 - R_max;  % R4 <= R_max
        R_min - R6;  % R6 >= R_min
        R6 - R_max;  % R6 <= R_max
        
        % 电容约束
        C_min - C1;  % C1 >= C_min
        C1 - C_max;  % C1 <= C_max
        C_min - C2;  % C2 >= C_min
        C2 - C_max;  % C2 <= C_max
        C_min - C3;  % C3 >= C_min
        C3 - C_max;  % C3 <= C_max
    ];
end
