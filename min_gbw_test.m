% min_gbw_test.m
% 此脚本用于寻找满足带宽要求的最小GBW值
% 使用二分搜索算法找到使滤波器-3dB带宽大于目标频率的最小GBW
% 作者：AI助手
% 日期：2025/6/5

clc;
clear;
close all;

fprintf('=== 最小GBW测试工具 ===\n');

%% ========== 用户可配置参数区域 ==========
% 在这里修改您的设计参数

% --- 测试参数 ---
target_bandwidth = 510e3;  % 目标-3dB带宽 (Hz) - 默认510kHz
CL = 4;                    % 负载电容 (pF)
min_gbw = 1e6;             % 最小测试GBW值 (Hz)
max_gbw = 20e6;            % 最大测试GBW值 (Hz)
tolerance = 1e3;           % 带宽容差 (Hz)
max_iterations = 10;       % 最大迭代次数

% 显示当前使用的参数值
fprintf('\n==========================================\n');
fprintf('使用的测试参数:\n');
fprintf('  目标带宽 = %.2f kHz\n', target_bandwidth/1e3);
fprintf('  负载电容 = %.2f pF\n', CL);
fprintf('  GBW搜索范围 = %.2f MHz - %.2f MHz\n', min_gbw/1e6, max_gbw/1e6);
fprintf('  带宽容差 = %.2f kHz\n', tolerance/1e3);
fprintf('==========================================\n\n');

%% 二分搜索算法实现

% 初始化搜索范围
low_gbw = min_gbw;
high_gbw = max_gbw;
best_gbw = max_gbw;  % 默认使用最大值
best_bandwidth = 0;

% 创建结果表格
results = cell(max_iterations+1, 5);
results{1,1} = '迭代';
results{1,2} = 'GBW (MHz)';
results{1,3} = '带宽 (kHz)';
results{1,4} = '目标 (kHz)';
results{1,5} = '结果';

fprintf('开始二分搜索...\n\n');
fprintf('%-6s %-10s %-12s %-12s %-10s\n', '迭代', 'GBW (MHz)', '带宽 (kHz)', '目标 (kHz)', '结果');
fprintf('%-6s %-10s %-12s %-12s %-10s\n', '------', '----------', '------------', '------------', '----------');

iteration = 1;
while (high_gbw - low_gbw) > tolerance && iteration <= max_iterations
    % 计算中间点
    mid_gbw = (low_gbw + high_gbw) / 2;
    
    % 设置当前测试的GBW值
    assignin('base', 'GBW_user', mid_gbw);
    
    % 运行集成设计脚本
    try
        % 使用evalc抑制输出
        evalc('run(''lpf_integrated_design.m'')');
        
        % 从仿真结果文件中读取-3dB带宽
        if exist('LPF.lis', 'file')
            fileContent = fileread('LPF.lis');
            lines = strsplit(fileContent, '\n');
            
            % 初始化变量
            fc3db = NaN;
            
            % 遍历文件的每一行
            for i = 1:length(lines)
                line = strtrim(lines{i});
                
                % 查找-3dB带宽 (fc3db)
                if contains(line, 'fc3db=')
                    tokens = regexp(line, 'fc3db=\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', 'tokens');
                    if ~isempty(tokens)
                        fc3db = str2double(tokens{1}{1});
                    end
                end
            end
            
            % 检查是否满足带宽要求
            if ~isnan(fc3db)
                current_bandwidth = fc3db;
                
                % 更新最佳GBW值
                if current_bandwidth >= target_bandwidth && mid_gbw < best_gbw
                    best_gbw = mid_gbw;
                    best_bandwidth = current_bandwidth;
                end
                
                % 调整搜索范围
                if current_bandwidth >= target_bandwidth
                    high_gbw = mid_gbw;  % 带宽满足要求，尝试更小的GBW
                    result = '满足要求';
                else
                    low_gbw = mid_gbw;   % 带宽不满足要求，尝试更大的GBW
                    result = '不满足要求';
                end
                
                % 记录结果
                results{iteration+1,1} = iteration;
                results{iteration+1,2} = mid_gbw/1e6;
                results{iteration+1,3} = current_bandwidth/1e3;
                results{iteration+1,4} = target_bandwidth/1e3;
                results{iteration+1,5} = result;
                
                % 打印结果
                fprintf('%-6d %-10.2f %-12.2f %-12.2f %-10s\n', iteration, mid_gbw/1e6, current_bandwidth/1e3, target_bandwidth/1e3, result);
            else
                fprintf('✗ 警告: 无法从LPF.lis文件中读取带宽\n');
                % 保守策略，假设带宽不满足要求
                low_gbw = mid_gbw;
            end
        else
            fprintf('✗ 警告: LPF.lis文件不存在\n');
            % 保守策略，假设带宽不满足要求
            low_gbw = mid_gbw;
        end
    catch ME
        fprintf('✗ 运行集成设计脚本失败: %s\n', ME.message);
        % 保守策略，假设带宽不满足要求
        low_gbw = mid_gbw;
    end
    
    iteration = iteration + 1;
end

% 最终结果
fprintf('\n=== 搜索完成 ===\n');
if best_bandwidth >= target_bandwidth
    fprintf('找到满足要求的最小GBW: %.2f MHz\n', best_gbw/1e6);
    fprintf('对应的-3dB带宽: %.2f kHz (目标: %.2f kHz)\n', best_bandwidth/1e3, target_bandwidth/1e3);
    
    % 设置最终的GBW值
    assignin('base', 'GBW_user', best_gbw);
    fprintf('已将GBW_user设置为%.2f MHz，可以运行lpf_integrated_design.m进行最终设计\n', best_gbw/1e6);
else
    fprintf('在给定的GBW范围内未找到满足要求的值\n');
    fprintf('最大测试GBW: %.2f MHz，对应的-3dB带宽: %.2f kHz (目标: %.2f kHz)\n', max_gbw/1e6, best_bandwidth/1e3, target_bandwidth/1e3);
end

% 将结果保存到文件
save('min_gbw_test_results.mat', 'results', 'best_gbw', 'best_bandwidth', 'target_bandwidth');

% 生成简单的文本报告
fid = fopen('min_gbw_test_report.txt', 'w');
fprintf(fid, '最小GBW测试报告\n');
fprintf(fid, '===================================\n\n');
fprintf(fid, '测试日期: %s\n', datestr(now));
fprintf(fid, '目标带宽: %.2f kHz\n', target_bandwidth/1e3);
fprintf(fid, '负载电容: %.2f pF\n', CL);
fprintf(fid, 'GBW搜索范围: %.2f MHz - %.2f MHz\n', min_gbw/1e6, max_gbw/1e6);
fprintf(fid, '带宽容差: %.2f kHz\n\n', tolerance/1e3);
fprintf(fid, '测试结果:\n');
fprintf(fid, '找到满足要求的最小GBW: %.2f MHz\n', best_gbw/1e6);
fprintf(fid, '对应的-3dB带宽: %.2f kHz (目标: %.2f kHz)\n', best_bandwidth/1e3, target_bandwidth/1e3);
fprintf(fid, '安全裕度: %.2f kHz (%.2f%%)\n\n', best_bandwidth-target_bandwidth, 100*(best_bandwidth-target_bandwidth)/target_bandwidth);
fprintf(fid, '详细测试数据:\n');
fprintf(fid, '%-6s %-10s %-12s %-12s %-10s\n', '迭代', 'GBW (MHz)', '带宽 (kHz)', '目标 (kHz)', '结果');
fprintf(fid, '%-6s %-10s %-12s %-12s %-10s\n', '------', '----------', '------------', '------------', '----------');
for i = 2:size(results, 1)
    fprintf(fid, '%-6d %-10.2f %-12.2f %-12.2f %-10s\n', results{i,1}, results{i,2}, results{i,3}, results{i,4}, results{i,5});
end
fclose(fid);

fprintf('结果报告已保存到 min_gbw_test_report.txt\n');
fprintf('测试数据已保存到 min_gbw_test_results.mat\n');

% 绘制GBW vs 带宽关系图
if iteration > 1
    figure('Name', 'GBW vs 带宽关系', 'NumberTitle', 'off');
    
    % 提取有效数据
    valid_rows = 2:min(iteration, size(results, 1));
    gbw_values = zeros(length(valid_rows), 1);
    bandwidth_values = zeros(length(valid_rows), 1);
    
    for i = 1:length(valid_rows)
        row = valid_rows(i);
        gbw_values(i) = results{row, 2};
        bandwidth_values(i) = results{row, 3};
    end
    
    % 绘制图形
    plot(gbw_values, bandwidth_values, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
    hold on;
    plot([min_gbw/1e6, max_gbw/1e6], [target_bandwidth/1e3, target_bandwidth/1e3], 'r--', 'LineWidth', 1.5);
    plot(best_gbw/1e6, best_bandwidth/1e3, 'ro', 'MarkerSize', 10, 'LineWidth', 2);
    grid on;
    xlabel('GBW (MHz)');
    ylabel('-3dB带宽 (kHz)');
    title('运放GBW vs 滤波器-3dB带宽关系');
    legend('测试结果', '目标带宽', '最优解', 'Location', 'southeast');
    saveas(gcf, 'gbw_bandwidth_relationship.png');
    fprintf('GBW vs 带宽关系图已保存到 gbw_bandwidth_relationship.png\n');
end 