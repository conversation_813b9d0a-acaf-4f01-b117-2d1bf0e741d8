% run_opamp_design.m
% 单独运行运放设计与优化脚本
% 此脚本允许用户调整运放设计指标，然后运行优化
% 作者：AI助手
% 日期：2025/6/5

clc;
clear;
close all;

fprintf('=== 运算放大器设计与优化工具 ===\n');

%% ========== 用户可配置参数区域 ==========
% 在这里修改您的设计参数

% --- 运放设计参数 ---
GBW = 130e6;        % 增益带宽积 (Hz) - 可根据需要修改
CL = 4;            % 负载电容 (pF) - 可根据需要修改

% 显示当前使用的参数值
fprintf('\n==========================================\n');
fprintf('使用的设计参数:\n');
fprintf('  GBW = %.2f MHz\n', GBW/1e6);
fprintf('  CL = %.2f pF\n', CL);
fprintf('==========================================\n\n');

%% 参数传递与优化执行

% 声明全局变量用于参数传递
global GBW_GLOBAL CL_GLOBAL;

% 设置全局变量
GBW_GLOBAL = GBW;
CL_GLOBAL = CL;

% 同时使用assignin方式传递参数（双保险）
assignin('base', 'GBW_user', GBW);
assignin('base', 'CL_user', CL);

% 创建临时参数文件作为第三种传递方式
save('temp_params.mat', 'GBW', 'CL');
fprintf('已保存参数到临时文件temp_params.mat\n');

% 确保重新运行运放设计脚本
if exist('opamp_design_results.mat', 'file')
    delete('opamp_design_results.mat');
    fprintf('已删除旧的opamp_design_results.mat文件，将重新生成\n');
end

% 运行运放设计脚本
fprintf('\n运行运放设计脚本...\n');
try
    % 使用run直接运行脚本，确保使用最新的参数
    run('opamp_design_with_optimization.m');
    fprintf('✓ 运放设计脚本执行完成\n');
    
    % 验证参数是否被正确使用
    if exist('opamp_design_results.mat', 'file')
        opamp_data = load('opamp_design_results.mat');
        fprintf('\n运放设计结果摘要:\n');
        
        % 显示器件尺寸
        fprintf('  器件尺寸:\n');
        fprintf('    W0 = %.2f μm (PMOS 电流镜)\n', opamp_data.W0);
        fprintf('    W1 = %.2f μm (NMOS 输入管)\n', opamp_data.W1);
        fprintf('    W2 = %.2f μm (PMOS 第二级)\n', opamp_data.W2);
        fprintf('    W3 = %.2f μm (NMOS 电流镜)\n', opamp_data.W3);
        fprintf('    W5 = %.2f μm (NMOS 电流镜，与W3匹配)\n', opamp_data.W5);
        fprintf('    W7 = %.2f μm (NMOS 第二级电流源)\n', opamp_data.W7);
        fprintf('    L = %.2f μm (所有器件)\n', opamp_data.L);
        
        % 显示电路参数
        fprintf('  电路参数:\n');
        fprintf('    Ibias = %.2f μA\n', opamp_data.Ibias);
        fprintf('    Cc = %.2f pF\n', opamp_data.Cc);
        fprintf('    Rc = %.3f kΩ\n', opamp_data.Rc);
        
        % 显示性能指标
        if isfield(opamp_data, 'UGB') && isfield(opamp_data, 'PM')
            fprintf('  性能指标:\n');
            fprintf('    单位增益带宽(UGB): %.2f MHz\n', opamp_data.UGB/1e6);
            fprintf('    相位裕度(PM): %.2f 度\n', opamp_data.PM);
        end
    else
        fprintf('✗ 警告: opamp_design_results.mat文件未创建\n');
    end
catch ME
    fprintf('✗ 运放设计脚本执行失败: %s\n', ME.message);
    fprintf('错误位置: %s 行 %d\n', ME.stack(1).name, ME.stack(1).line);
end

fprintf('\n=== 设计完成 ===\n'); 